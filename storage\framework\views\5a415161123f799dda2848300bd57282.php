<?php if (isset($component)) { $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.layouts.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> 
        <?php echo app('translator')->get('admin::app.catalog.attributes.index.title'); ?>
     <?php $__env->endSlot(); ?>

    <!-- Standard Bagisto Admin Header -->
    <div class="flex items-center justify-between gap-4 max-sm:flex-wrap">
        <!-- Title -->
        <p class="text-xl font-bold text-gray-800 dark:text-white">
            <?php echo app('translator')->get('admin::app.subscriptions.index.title'); ?>
        </p>

        <!-- Action Buttons -->
        <div class="flex items-center gap-x-2.5">
            <?php echo view_render_event(eventName: 'bagisto.admin.catalog.products.create.before'); ?>


            <?php
                $currentUser = auth()->guard('admin')->user() ?: auth()->guard('vendor')->user();
                $isAdmin = $currentUser && $currentUser->role_id == 1;
                $isVendor = $currentUser && $currentUser->role_id == 2;
            ?>

            <?php if(bouncer()->hasPermission('catalog.products.create') && $isAdmin): ?>
                <v-create-product-form>
                    <a
                        href="<?php echo e(route('admin.subscriptions.create')); ?>"
                        class="primary-button"
                    >
                        <?php echo app('translator')->get('admin::app.subscriptions.index.create-plan-button'); ?>
                    </a>
                </v-create-product-form>
            <?php endif; ?>

            <?php if($isVendor): ?>
                <a
                    href="<?php echo e(route('admin.vendor-plans.index')); ?>"
                    class="secondary-button"
                >
                    <span class="icon-vendor-plans"></span>
                    <?php echo app('translator')->get('admin::app.subscriptions.index.your-plans'); ?>
                </a>
            <?php endif; ?>

            <?php echo view_render_event('bagisto.admin.catalog.products.create.after'); ?>

        </div>
    </div>

    <?php echo view_render_event('bagisto.admin.catalog.attributes.list.before'); ?>


    <?php if (isset($component)) { $__componentOriginal3bea17ac3f7235e71a823454ccb74424 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3bea17ac3f7235e71a823454ccb74424 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'admin::components.datagrid.index','data' => ['src' => route('admin.subscriptions.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin::datagrid'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['src' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.subscriptions.index'))]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3bea17ac3f7235e71a823454ccb74424)): ?>
<?php $attributes = $__attributesOriginal3bea17ac3f7235e71a823454ccb74424; ?>
<?php unset($__attributesOriginal3bea17ac3f7235e71a823454ccb74424); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3bea17ac3f7235e71a823454ccb74424)): ?>
<?php $component = $__componentOriginal3bea17ac3f7235e71a823454ccb74424; ?>
<?php unset($__componentOriginal3bea17ac3f7235e71a823454ccb74424); ?>
<?php endif; ?>

    <?php echo view_render_event('bagisto.admin.catalog.attributes.list.after'); ?>


    <?php
        $isVendor = auth()->guard('vendor')->check();
    ?>

    <?php if($isVendor): ?>
        <!-- Responsive Bagisto Admin Modal -->
        <div id="subscription-modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 p-4">
            <div class="w-full max-w-sm mx-auto rounded-lg bg-white shadow-lg dark:bg-gray-800 max-h-[90vh] overflow-y-auto">
                <!-- Modal Header -->
                <div class="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 p-4">
                    <div class="flex items-center space-x-2">
                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
                            <i class="icon-subscription text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">Subscribe to Plan</h3>
                    </div>
                    <button
                        onclick="closeSubscriptionModal()"
                        class="flex h-8 w-8 items-center justify-center rounded-full text-gray-400 hover:bg-gray-100 hover:text-gray-600 dark:hover:bg-gray-700 transition-colors"
                    >
                        <i class="icon-cancel text-lg"></i>
                    </button>
                </div>

                <!-- Modal Body -->
                <div class="p-4">
                    <!-- Plan Details Card -->
                    <div id="plan-details" class="mb-4">
                        <div class="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-700">
                            <div class="text-center">
                                <h4 id="plan-name" class="text-lg font-semibold text-gray-800 dark:text-white mb-2"></h4>
                                <div class="flex items-center justify-center space-x-1 mb-2">
                                    <span class="text-sm text-gray-500 dark:text-gray-400">₹</span>
                                    <p id="plan-price" class="text-2xl font-bold text-blue-600 dark:text-blue-400"></p>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-300">One-time payment</p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Info -->
                    <div class="mb-4 rounded-lg bg-blue-50 p-3 dark:bg-blue-900/20">
                        <div class="flex items-start space-x-2">
                            <i class="icon-info text-blue-600 dark:text-blue-400 mt-0.5"></i>
                            <div>
                                <p class="text-sm font-medium text-blue-800 dark:text-blue-300">Secure Payment</p>
                                <p class="text-xs text-blue-600 dark:text-blue-400">Your payment is processed securely through Razorpay</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="border-t border-gray-200 dark:border-gray-700 p-4">
                    <div class="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
                        <button
                            onclick="closeSubscriptionModal()"
                            class="flex-1 rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                        >
                            Cancel
                        </button>
                        <button
                            onclick="initiateRazorpayPayment()"
                            class="flex-1 rounded-md bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors"
                        >
                            <span class="flex items-center justify-center space-x-2">
                                <i class="icon-payment text-sm"></i>
                                <span>Pay Now</span>
                            </span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Modal Styles -->
        <style>
            /* Modal Animation */
            #subscription-modal {
                backdrop-filter: blur(4px);
                animation: fadeIn 0.3s ease-out;
            }

            #subscription-modal > div {
                animation: slideIn 0.3s ease-out;
                transform-origin: center;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes fadeOut {
                from { opacity: 1; }
                to { opacity: 0; }
            }

            @keyframes slideIn {
                from {
                    opacity: 0;
                    transform: scale(0.9) translateY(-20px);
                }
                to {
                    opacity: 1;
                    transform: scale(1) translateY(0);
                }
            }

            /* Mobile Responsive Adjustments */
            @media (max-width: 640px) {
                #subscription-modal > div {
                    margin: 0.5rem;
                    max-width: calc(100vw - 1rem);
                }

                #subscription-modal .flex-col {
                    flex-direction: column;
                }

                #subscription-modal button {
                    width: 100%;
                    margin-bottom: 0.5rem;
                }
            }

            /* Button Hover Effects */
            .payment-button:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            }

            /* Loading State */
            .loading {
                position: relative;
                pointer-events: none;
            }

            .loading::after {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                margin: -8px 0 0 -8px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* Enhanced Plan Details */
            #plan-details .rounded-lg {
                transition: all 0.3s ease;
            }

            #plan-details .rounded-lg:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
        </style>

        <!-- Razorpay Script -->
        <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

        <script>
            let currentPlan = null;

            function subscribeToplan(planId, planName, planPrice) {
                console.log('Subscribe function called with:', { planId, planName, planPrice });

                currentPlan = {
                    id: planId,
                    name: planName,
                    price: planPrice
                };

                // Update modal content
                document.getElementById('plan-name').textContent = planName;
                document.getElementById('plan-price').textContent = planPrice;

                // Show modal with animation
                const modal = document.getElementById('subscription-modal');
                modal.classList.remove('hidden');
                modal.classList.add('flex');

                // Prevent body scroll when modal is open
                document.body.style.overflow = 'hidden';

                console.log('Modal should be visible now');
            }



            function closeSubscriptionModal() {
                const modal = document.getElementById('subscription-modal');

                // Add fade out animation
                modal.style.animation = 'fadeOut 0.3s ease-out';

                setTimeout(() => {
                    modal.classList.add('hidden');
                    modal.classList.remove('flex');
                    modal.style.animation = '';

                    // Restore body scroll
                    document.body.style.overflow = '';

                    currentPlan = null;
                }, 300);
            }

            // Close modal when clicking outside
            document.addEventListener('click', function(event) {
                const modal = document.getElementById('subscription-modal');
                if (event.target === modal) {
                    closeSubscriptionModal();
                }
            });

            // Close modal with Escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    const modal = document.getElementById('subscription-modal');
                    if (!modal.classList.contains('hidden')) {
                        closeSubscriptionModal();
                    }
                }
            });

            function initiateRazorpayPayment() {
                if (!currentPlan) {
                    alert('Please select a plan first');
                    return;
                }

                console.log('Initiating payment for plan:', currentPlan);

                // Add loading state to button
                const payButton = document.querySelector('#subscription-modal button[onclick="initiateRazorpayPayment()"]');
                const originalText = payButton.innerHTML;
                payButton.classList.add('loading');
                payButton.innerHTML = '<span>Processing...</span>';
                payButton.disabled = true;

                // Check if Razorpay is loaded
                if (typeof Razorpay === 'undefined') {
                    alert('Razorpay is not loaded. Please refresh the page and try again.');
                    // Remove loading state
                    payButton.classList.remove('loading');
                    payButton.innerHTML = originalText;
                    payButton.disabled = false;
                    return;
                }

                const razorpayKey = "<?php echo e(config('services.razorpay.key')); ?>";
                if (!razorpayKey) {
                    alert('Razorpay configuration is missing. Please contact administrator.');
                    // Remove loading state
                    payButton.classList.remove('loading');
                    payButton.innerHTML = originalText;
                    payButton.disabled = false;
                    return;
                }

                const options = {
                    key: razorpayKey,
                    amount: currentPlan.price * 100, // Amount in paise
                    currency: "INR",
                    name: "Geniusmart",
                    description: "Subscription Plan: " + currentPlan.name,
                    image: "<?php echo e(asset('packages/Webkul/Admin/src/Resources/assets/images/logo.svg')); ?>",
                    handler: function (response) {
                        console.log('Payment successful:', response);
                        processSubscription(response);
                    },
                    prefill: {
                        name: "<?php echo e(auth()->guard('vendor')->user()->company_name ?? ''); ?>",
                        email: "<?php echo e(auth()->guard('vendor')->user()->company_email ?? ''); ?>",
                        contact: "<?php echo e(auth()->guard('vendor')->user()->company_phone ?? ''); ?>"
                    },
                    theme: {
                        color: "#667eea"
                    },
                    modal: {
                        ondismiss: function() {
                            console.log('Payment modal closed by user');
                            // Remove loading state
                            payButton.classList.remove('loading');
                            payButton.innerHTML = originalText;
                            payButton.disabled = false;
                        }
                    }
                };

                console.log('Razorpay options:', options);

                try {
                    const rzp = new Razorpay(options);
                    rzp.open();
                } catch (error) {
                    console.error('Error opening Razorpay:', error);
                    alert('Error opening payment gateway: ' + error.message);
                }
            }

            function processSubscription(paymentResponse) {
                console.log('Processing subscription with payment response:', paymentResponse);

                // Get CSRF token
                const csrfToken = document.querySelector('meta[name="csrf-token"]');
                if (!csrfToken) {
                    alert('CSRF token not found. Please refresh the page and try again.');
                    return;
                }

                // Send payment details to server for verification and subscription creation
                fetch("<?php echo e(route('admin.subscriptions.process-payment')); ?>", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': csrfToken.getAttribute('content'),
                        'Accept': 'application/json',
                    },
                    body: JSON.stringify({
                        plan_id: currentPlan.id,
                        payment_id: paymentResponse.razorpay_payment_id,
                        signature: paymentResponse.razorpay_signature || '',
                        order_id: paymentResponse.razorpay_order_id || ''
                    })
                })
                .then(response => {
                    console.log('Server response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Server response data:', data);
                    if (data.success) {
                        alert('🎉 Subscription successful! You can now access the plan features.');
                        closeSubscriptionModal();
                        window.location.reload();
                    } else {
                        alert('❌ Subscription failed: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error processing subscription:', error);
                    alert('❌ An error occurred while processing your subscription. Please try again.');
                });
            }

            // Close modal when clicking outside
            document.getElementById('subscription-modal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeSubscriptionModal();
                }
            });
        </script>
    <?php endif; ?>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $attributes = $__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__attributesOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1)): ?>
<?php $component = $__componentOriginal8001c520f4b7dcb40a16cd3b411856d1; ?>
<?php unset($__componentOriginal8001c520f4b7dcb40a16cd3b411856d1); ?>
<?php endif; ?>
<?php /**PATH D:\venkat clone repo\geniusmart\mart\digitalmartgenius\packages\Webkul\Admin\src/resources/views/subscription/index.blade.php ENDPATH**/ ?>