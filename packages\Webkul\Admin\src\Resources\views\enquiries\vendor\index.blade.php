<x-admin::layouts>
    <x-slot:title>
        @lang('admin::app.enquiries.vendor.index.title')
    </x-slot>

    <div class="flex items-center justify-between gap-4 max-sm:flex-wrap">
        <!-- Title -->
        <p class="text-xl font-bold text-gray-800 dark:text-white">
            @lang('admin::app.enquiries.vendor.index.title')
        </p>

        <!-- Stats Cards -->
        <div class="flex gap-4">
            <div class="rounded-lg bg-blue-50 px-4 py-2 text-center">
                <p class="text-sm text-blue-600">Total</p>
                <p class="text-lg font-bold text-blue-800" id="total-enquiries">-</p>
            </div>
            <div class="rounded-lg bg-yellow-50 px-4 py-2 text-center">
                <p class="text-sm text-yellow-600">Pending</p>
                <p class="text-lg font-bold text-yellow-800" id="pending-enquiries">-</p>
            </div>
            <div class="rounded-lg bg-green-50 px-4 py-2 text-center">
                <p class="text-sm text-green-600">In Progress</p>
                <p class="text-lg font-bold text-green-800" id="progress-enquiries">-</p>
            </div>
            <div class="rounded-lg bg-gray-50 px-4 py-2 text-center">
                <p class="text-sm text-gray-600">Closed</p>
                <p class="text-lg font-bold text-gray-800" id="closed-enquiries">-</p>
            </div>
        </div>
    </div>

    <!-- Help Text -->
    <div class="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4">
        <div class="flex items-start gap-3">
            <svg class="mt-0.5 h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h4 class="font-medium text-blue-800">Product Enquiries</h4>
                <p class="mt-1 text-sm text-blue-700">
                    Here you can view and manage enquiries for your products. You can update the status of enquiries and respond to customer questions.
                </p>
            </div>
        </div>
    </div>

    {!! view_render_event('bagisto.admin.enquiries.vendor.list.before') !!}

    <x-admin::datagrid :src="route('admin.enquiries.vendor.index')" />

    {!! view_render_event('bagisto.admin.enquiries.vendor.list.after') !!}

</x-admin::layouts>

@pushOnce('scripts')
<script>
    // Load enquiry statistics
    document.addEventListener('DOMContentLoaded', function() {
        fetch('{{ route("admin.enquiries.vendor.stats") }}')
            .then(response => response.json())
            .then(data => {
                if (data.status) {
                    document.getElementById('total-enquiries').textContent = data.data.total || 0;
                    document.getElementById('pending-enquiries').textContent = data.data.pending || 0;
                    document.getElementById('progress-enquiries').textContent = data.data.in_progress || 0;
                    document.getElementById('closed-enquiries').textContent = data.data.closed || 0;
                }
            })
            .catch(error => {
                console.error('Error loading enquiry stats:', error);
            });
    });
</script>
@endPushOnce
