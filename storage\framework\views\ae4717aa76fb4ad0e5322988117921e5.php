<v-product-card
    <?php echo e($attributes); ?>

    :product="product"
>
</v-product-card>

<?php if (! $__env->hasRenderedOnce('67af9711-2eb5-41c5-a555-647d635fa747')): $__env->markAsRenderedOnce('67af9711-2eb5-41c5-a555-647d635fa747');
$__env->startPush('scripts'); ?>
    <script
        type="text/x-template"
        id="v-product-card-template"
    >
        <!-- Grid Card -->
        <div
            class="1180:transtion-all group w-full rounded-md 1180:relative 1180:grid 1180:content-start 1180:overflow-hidden 1180:duration-300 1180:hover:shadow-[0_5px_10px_rgba(0,0,0,0.1)]"
            v-if="mode != 'list'"
        >
            <div class="relative max-h-[300px] max-w-[291px] overflow-hidden max-md:max-h-60 max-md:max-w-full max-md:rounded-lg max-sm:max-h-[200px] max-sm:max-w-full">
                <?php echo view_render_event('bagisto.shop.components.products.card.image.before'); ?>


                <!-- Product Image -->
                <a
                    :href="`<?php echo e(route('shop.product_or_category.index', '')); ?>/${product.url_key}`"
                    :aria-label="product.name + ' '"
                >
                    <?php if (isset($component)) { $__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.media.images.lazy','data' => ['class' => 'after:content-[\' \'] relative bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105',':src' => 'product.base_image.medium_image_url',':key' => 'product.id',':index' => 'product.id','width' => '291','height' => '300',':alt' => 'product.name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::media.images.lazy'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'after:content-[\' \'] relative bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105',':src' => 'product.base_image.medium_image_url',':key' => 'product.id',':index' => 'product.id','width' => '291','height' => '300',':alt' => 'product.name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848)): ?>
<?php $attributes = $__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848; ?>
<?php unset($__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848)): ?>
<?php $component = $__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848; ?>
<?php unset($__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848); ?>
<?php endif; ?>
                </a>

                <?php echo view_render_event('bagisto.shop.components.products.card.image.after'); ?>

                
                <!-- Product Ratings -->
                <?php echo view_render_event('bagisto.shop.components.products.card.average_ratings.before'); ?>


                <?php if(core()->getConfigData('catalog.products.review.summary') == 'star_counts'): ?>
                    <?php if (isset($component)) { $__componentOriginal189c61a27640c2633434112e6503d31c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal189c61a27640c2633434112e6503d31c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.products.ratings','data' => ['class' => 'absolute bottom-1.5 items-center !border-white bg-white/80 !px-2 !py-1 text-xs max-sm:!px-1.5 max-sm:!py-0.5 ltr:left-1.5 rtl:right-1.5',':average' => 'product.ratings.average',':total' => 'product.ratings.total',':rating' => 'false','vIf' => 'product.ratings.total']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::products.ratings'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'absolute bottom-1.5 items-center !border-white bg-white/80 !px-2 !py-1 text-xs max-sm:!px-1.5 max-sm:!py-0.5 ltr:left-1.5 rtl:right-1.5',':average' => 'product.ratings.average',':total' => 'product.ratings.total',':rating' => 'false','v-if' => 'product.ratings.total']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $attributes = $__attributesOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__attributesOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $component = $__componentOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__componentOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
                <?php else: ?>
                    <?php if (isset($component)) { $__componentOriginal189c61a27640c2633434112e6503d31c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal189c61a27640c2633434112e6503d31c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.products.ratings','data' => ['class' => 'absolute bottom-1.5 items-center !border-white bg-white/80 !px-2 !py-1 text-xs max-sm:!px-1.5 max-sm:!py-0.5 ltr:left-1.5 rtl:right-1.5',':average' => 'product.ratings.average',':total' => 'product.reviews.total',':rating' => 'false','vIf' => 'product.reviews.total']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::products.ratings'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'absolute bottom-1.5 items-center !border-white bg-white/80 !px-2 !py-1 text-xs max-sm:!px-1.5 max-sm:!py-0.5 ltr:left-1.5 rtl:right-1.5',':average' => 'product.ratings.average',':total' => 'product.reviews.total',':rating' => 'false','v-if' => 'product.reviews.total']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $attributes = $__attributesOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__attributesOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $component = $__componentOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__componentOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
                <?php endif; ?>

                <?php echo view_render_event('bagisto.shop.components.products.card.average_ratings.after'); ?>


                <div class="action-items bg-black">
                    <!-- Product Sale Badge -->
                    <p
                        class="absolute top-1.5 inline-block rounded-[44px] bg-red-600 px-2.5 text-sm text-white max-sm:rounded-l-none max-sm:rounded-r-xl max-sm:px-2 max-sm:py-0.5 max-sm:text-xs ltr:left-1.5 max-sm:ltr:left-0 rtl:right-5 max-sm:rtl:right-0"
                        v-if="product.on_sale"
                    >
                        <?php echo app('translator')->get('shop::app.components.products.card.sale'); ?>
                    </p>

                    <!-- Product New Badge -->
                    <p
                        class="absolute top-1.5 inline-block rounded-[44px] bg-navyBlue px-2.5 text-sm text-white max-sm:rounded-l-none max-sm:rounded-r-xl max-sm:px-2 max-sm:py-0.5 max-sm:text-xs ltr:left-1.5 max-sm:ltr:left-0 rtl:right-1.5 max-sm:rtl:right-0"
                        v-else-if="product.is_new"
                    >
                        <?php echo app('translator')->get('shop::app.components.products.card.new'); ?>
                    </p>

                    <div class="opacity-0 transition-all duration-300 group-hover:bottom-0 group-hover:opacity-100 max-lg:opacity-100 max-sm:opacity-100">

                        <?php echo view_render_event('bagisto.shop.components.products.card.wishlist_option.before'); ?>


                        <?php if(core()->getConfigData('customer.settings.wishlist.wishlist_option')): ?>
                            <span
                                class="absolute top-2.5 flex h-6 w-6 items-center justify-center rounded-full border border-zinc-200 bg-white text-lg md:hidden ltr:right-1.5 rtl:left-1.5"
                                role="button"
                                aria-label="<?php echo app('translator')->get('shop::app.components.products.card.add-to-wishlist'); ?>"
                                tabindex="0"
                                :class="product.is_wishlist ? 'icon-heart-fill text-red-500' : 'icon-heart'"
                                @click="addToWishlist()"
                            >
                            </span>
                        <?php endif; ?>

                        <?php echo view_render_event('bagisto.shop.components.products.card.wishlist_option.after'); ?>


                        <?php echo view_render_event('bagisto.shop.components.products.card.compare_option.before'); ?>


                        <?php if(core()->getConfigData('catalog.products.settings.compare_option')): ?>
                            <span
                                class="icon-compare absolute top-10 flex h-6 w-6 items-center justify-center rounded-full border border-zinc-200 bg-white text-lg sm:hidden ltr:right-1.5 rtl:left-1.5"
                                role="button"
                                aria-label="<?php echo app('translator')->get('shop::app.components.products.card.add-to-compare'); ?>"
                                tabindex="0"
                                @click="addToCompare(product.id)"
                            >
                            </span>
                        <?php endif; ?>

                        <?php echo view_render_event('bagisto.shop.components.products.card.compare_option.after'); ?>


                    </div>
                </div>
            </div>

            <!-- Product Information Section -->
            <div class="-mt-9 grid max-w-[291px] translate-y-9 content-start gap-2.5 bg-white p-2.5 transition-transform duration-300 ease-out group-hover:-translate-y-0 group-hover:rounded-t-lg max-md:relative max-md:mt-0 max-md:translate-y-0 max-md:gap-0 max-md:px-0 max-md:py-1.5 max-sm:min-w-[170px] max-sm:max-w-[192px]">

                <?php echo view_render_event('bagisto.shop.components.products.card.name.before'); ?>


                <p class="break-all text-base font-medium max-md:mb-1.5 max-md:max-w-56 max-md:whitespace-break-spaces max-md:leading-6 max-sm:max-w-[192px] max-sm:text-sm max-sm:leading-4">
                    {{ product.name }}
                </p>

                <?php echo view_render_event('bagisto.shop.components.products.card.name.after'); ?>


                <!-- Pricing -->
                <?php echo view_render_event('bagisto.shop.components.products.card.price.before'); ?>


                <div
                    class="flex items-center gap-2.5 text-lg font-semibold max-sm:text-sm max-sm:leading-6"
                    v-html="product.price_html"
                >
                </div>

                <?php echo view_render_event('bagisto.shop.components.products.card.price.after'); ?>


                <!-- Product Actions Section -->
                <div class="action-items flex items-center justify-between opacity-0 transition-all duration-300 ease-in-out group-hover:opacity-100 max-md:hidden">
                    <?php if(core()->getConfigData('sales.checkout.shopping_cart.cart_page')): ?>
                        <?php echo view_render_event('bagisto.shop.components.products.card.add_to_cart.before'); ?>


                        <button
                            class="secondary-button w-full max-w-full p-2.5 text-sm font-medium max-sm:rounded-xl max-sm:p-2"
                            :disabled="! product.is_saleable || isAddingToCart"
                             @click="goToProduct(product.url_key)"
                        >
                          <?php echo app('translator')->get('shop::app.components.products.card.view-details'); ?>
                        </button>
                        <?php echo view_render_event('bagisto.shop.components.products.card.add_to_cart.after'); ?>

                    <?php endif; ?>

                    <?php echo view_render_event('bagisto.shop.components.products.card.wishlist_option.before'); ?>


                    <?php if(core()->getConfigData('customer.settings.wishlist.wishlist_option')): ?>
                        <span
                            class="cursor-pointer p-2.5 text-2xl max-sm:hidden"
                            role="button"
                            aria-label="<?php echo app('translator')->get('shop::app.components.products.card.add-to-wishlist'); ?>"
                            tabindex="0"
                            :class="product.is_wishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                            @click="addToWishlist()"
                        >
                        </span>
                    <?php endif; ?>

                    <?php echo view_render_event('bagisto.shop.components.products.card.wishlist_option.after'); ?>


                    <?php echo view_render_event('bagisto.shop.components.products.card.compare_option.before'); ?>


                    <?php if(core()->getConfigData('catalog.products.settings.compare_option')): ?>
                        <!-- <span
                            class="icon-compare cursor-pointer p-2.5 text-2xl max-sm:hidden"
                            role="button"
                            aria-label="<?php echo app('translator')->get('shop::app.components.products.card.add-to-compare'); ?>"
                            tabindex="0"
                            @click="addToCompare(product.id)"
                        >
                        </span> -->
                    <?php endif; ?>

                    <?php echo view_render_event('bagisto.shop.components.products.card.compare_option.after'); ?>

                </div>
            </div>
        </div>

        <!-- List Card -->
        <div
            class="relative flex max-w-max grid-cols-2 gap-4 overflow-hidden rounded max-sm:flex-wrap"
            v-else
        >
            <div class="group relative max-h-[258px] max-w-[250px] overflow-hidden"> 

                <?php echo view_render_event('bagisto.shop.components.products.card.image.before'); ?>


                <a :href="`<?php echo e(route('shop.product_or_category.index', '')); ?>/${product.url_key}`">
                    <?php if (isset($component)) { $__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.media.images.lazy','data' => ['class' => 'after:content-[\' \'] relative min-w-[250px] bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105',':src' => 'product.base_image.medium_image_url',':key' => 'product.id',':index' => 'product.id','width' => '291','height' => '300',':alt' => 'product.name']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::media.images.lazy'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'after:content-[\' \'] relative min-w-[250px] bg-zinc-100 transition-all duration-300 after:block after:pb-[calc(100%+9px)] group-hover:scale-105',':src' => 'product.base_image.medium_image_url',':key' => 'product.id',':index' => 'product.id','width' => '291','height' => '300',':alt' => 'product.name']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848)): ?>
<?php $attributes = $__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848; ?>
<?php unset($__attributesOriginal3657c70d06ebc8c078f4ecac2ea1a848); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848)): ?>
<?php $component = $__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848; ?>
<?php unset($__componentOriginal3657c70d06ebc8c078f4ecac2ea1a848); ?>
<?php endif; ?>
                </a>

                <?php echo view_render_event('bagisto.shop.components.products.card.image.after'); ?>


                <div class="action-items bg-black">
                    <p
                        class="absolute top-5 inline-block rounded-[44px] bg-red-500 px-2.5 text-sm text-white ltr:left-5 max-sm:ltr:left-2 rtl:right-5"
                        v-if="product.on_sale"
                    >
                        <?php echo app('translator')->get('shop::app.components.products.card.sale'); ?>
                    </p>

                    <p
                        class="absolute top-5 inline-block rounded-[44px] bg-navyBlue px-2.5 text-sm text-white ltr:left-5 max-sm:ltr:left-2 rtl:right-5"
                        v-else-if="product.is_new"
                    >
                        <?php echo app('translator')->get('shop::app.components.products.card.new'); ?>
                    </p>

                    <div class="opacity-0 transition-all duration-300 group-hover:bottom-0 group-hover:opacity-100 max-sm:opacity-100">

                        <?php echo view_render_event('bagisto.shop.components.products.card.wishlist_option.before'); ?>


                        <?php if(core()->getConfigData('customer.settings.wishlist.wishlist_option')): ?>
                            <span 
                                class="absolute top-5 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-md bg-white text-2xl ltr:right-5 rtl:left-5"
                                role="button"
                                aria-label="<?php echo app('translator')->get('shop::app.components.products.card.add-to-wishlist'); ?>"
                                tabindex="0"
                                :class="product.is_wishlist ? 'icon-heart-fill text-red-600' : 'icon-heart'"
                                @click="addToWishlist()"
                            >
                            </span>
                        <?php endif; ?>

                        <?php echo view_render_event('bagisto.shop.components.products.card.wishlist_option.after'); ?>


                        <?php echo view_render_event('bagisto.shop.components.products.card.compare_option.before'); ?>


                        <?php if(core()->getConfigData('catalog.products.settings.compare_option')): ?>
                            <span
                                class="icon-compare absolute top-16 flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-md bg-white text-2xl ltr:right-5 rtl:left-5"
                                role="button"
                                aria-label="<?php echo app('translator')->get('shop::app.components.products.card.add-to-compare'); ?>"
                                tabindex="0"
                                @click="addToCompare(product.id)"
                            >
                            </span>
                        <?php endif; ?>

                        <?php echo view_render_event('bagisto.shop.components.products.card.compare_option.after'); ?>

                    </div>
                </div>
            </div>

            <div class="grid content-start gap-4">

                <?php echo view_render_event('bagisto.shop.components.products.card.name.before'); ?>


                <p class="text-base">
                    {{ product.name }}
                </p>

                <?php echo view_render_event('bagisto.shop.components.products.card.name.after'); ?>


                <?php echo view_render_event('bagisto.shop.components.products.card.price.before'); ?>


                <div
                    class="flex gap-2.5 text-lg font-semibold"
                    v-html="product.price_html"
                >
                </div>

                <?php echo view_render_event('bagisto.shop.components.products.card.price.after'); ?>


                <!-- Needs to implement that in future -->
                <div class="flex hidden gap-4">
                    <span class="block h-[30px] w-[30px] rounded-full bg-[#B5DCB4]">
                    </span>

                    <span class="block h-[30px] w-[30px] rounded-full bg-zinc-500">
                    </span>
                </div>

                <?php echo view_render_event('bagisto.shop.components.products.card.average_ratings.before'); ?>


                <p class="text-sm text-zinc-500">
                    <template  v-if="! product.ratings.total">
                        <p class="text-sm text-zinc-500">
                            <?php echo app('translator')->get('shop::app.components.products.card.review-description'); ?>
                        </p>
                    </template>

                    <template v-else>
                        <?php if(core()->getConfigData('catalog.products.review.summary') == 'star_counts'): ?>
                            <?php if (isset($component)) { $__componentOriginal189c61a27640c2633434112e6503d31c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal189c61a27640c2633434112e6503d31c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.products.ratings','data' => [':average' => 'product.ratings.average',':total' => 'product.ratings.total',':rating' => 'false']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::products.ratings'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([':average' => 'product.ratings.average',':total' => 'product.ratings.total',':rating' => 'false']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $attributes = $__attributesOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__attributesOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $component = $__componentOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__componentOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
                        <?php else: ?>
                            <?php if (isset($component)) { $__componentOriginal189c61a27640c2633434112e6503d31c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal189c61a27640c2633434112e6503d31c = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.products.ratings','data' => [':average' => 'product.ratings.average',':total' => 'product.reviews.total',':rating' => 'false']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::products.ratings'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([':average' => 'product.ratings.average',':total' => 'product.reviews.total',':rating' => 'false']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $attributes = $__attributesOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__attributesOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal189c61a27640c2633434112e6503d31c)): ?>
<?php $component = $__componentOriginal189c61a27640c2633434112e6503d31c; ?>
<?php unset($__componentOriginal189c61a27640c2633434112e6503d31c); ?>
<?php endif; ?>
                        <?php endif; ?>
                    </template>
                </p>

                <?php echo view_render_event('bagisto.shop.components.products.card.average_ratings.after'); ?>


                <?php if(core()->getConfigData('sales.checkout.shopping_cart.cart_page')): ?>

                    <?php echo view_render_event('bagisto.shop.components.products.card.add_to_cart.before'); ?>


                    <?php if (isset($component)) { $__componentOriginal30786825665921390a816ebee82cf580 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal30786825665921390a816ebee82cf580 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.button.index','data' => ['class' => 'primary-button whitespace-nowrap px-8 py-2.5','title' => trans('shop::app.components.products.card.add-to-cart'),':loading' => 'isAddingToCart',':disabled' => '! product.is_saleable || isAddingToCart','@click' => 'addToCart()']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'primary-button whitespace-nowrap px-8 py-2.5','title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('shop::app.components.products.card.add-to-cart')),':loading' => 'isAddingToCart',':disabled' => '! product.is_saleable || isAddingToCart','@click' => 'addToCart()']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal30786825665921390a816ebee82cf580)): ?>
<?php $attributes = $__attributesOriginal30786825665921390a816ebee82cf580; ?>
<?php unset($__attributesOriginal30786825665921390a816ebee82cf580); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal30786825665921390a816ebee82cf580)): ?>
<?php $component = $__componentOriginal30786825665921390a816ebee82cf580; ?>
<?php unset($__componentOriginal30786825665921390a816ebee82cf580); ?>
<?php endif; ?>

                    <?php echo view_render_event('bagisto.shop.components.products.card.add_to_cart.after'); ?>


                <?php endif; ?>
            </div>
        </div>
    </script>

    <script type="module">
        app.component('v-product-card', {
            template: '#v-product-card-template',

            props: ['mode', 'product'],

            data() {
                return {
                    isCustomer: '<?php echo e(auth()->guard('customer')->check()); ?>',

                    isAddingToCart: false,
                }
            },

            methods: {
                addToWishlist() {
                    if (this.isCustomer) {
                        this.$axios.post(`<?php echo e(route('shop.api.customers.account.wishlist.store')); ?>`, {
                                product_id: this.product.id
                            })
                            .then(response => {
                                this.product.is_wishlist = ! this.product.is_wishlist;

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                            })
                            .catch(error => {});
                        } else {
                            window.location.href = "<?php echo e(route('shop.customer.session.index')); ?>";
                        }
                },

                addToCompare(productId) {
                    /**
                     * This will handle for customers.
                     */
                    if (this.isCustomer) {
                        this.$axios.post('<?php echo e(route("shop.api.compare.store")); ?>', {
                                'product_id': productId
                            })
                            .then(response => {
                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.data.message });
                            })
                            .catch(error => {
                                if ([400, 422].includes(error.response.status)) {
                                    this.$emitter.emit('add-flash', { type: 'warning', message: error.response.data.data.message });

                                    return;
                                }

                                this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message});
                            });

                        return;
                    }

                    /**
                     * This will handle for guests.
                     */
                    let items = this.getStorageValue() ?? [];

                    if (items.length) {
                        if (! items.includes(productId)) {
                            items.push(productId);

                            localStorage.setItem('compare_items', JSON.stringify(items));

                            this.$emitter.emit('add-flash', { type: 'success', message: "<?php echo app('translator')->get('shop::app.components.products.card.add-to-compare-success'); ?>" });
                        } else {
                            this.$emitter.emit('add-flash', { type: 'warning', message: "<?php echo app('translator')->get('shop::app.components.products.card.already-in-compare'); ?>" });
                        }
                    } else {
                        localStorage.setItem('compare_items', JSON.stringify([productId]));

                        this.$emitter.emit('add-flash', { type: 'success', message: "<?php echo app('translator')->get('shop::app.components.products.card.add-to-compare-success'); ?>" });

                    }
                },

                getStorageValue(key) {
                    let value = localStorage.getItem('compare_items');

                    if (! value) {
                        return [];
                    }

                    return JSON.parse(value);
                },

                addToCart() {
                    this.isAddingToCart = true;

                    this.$axios.post('<?php echo e(route("shop.api.checkout.cart.store")); ?>', {
                            'quantity': 1,
                            'product_id': this.product.id,
                        })
                        .then(response => {
                            if (response.data.message) {
                                this.$emitter.emit('update-mini-cart', response.data.data );

                                this.$emitter.emit('add-flash', { type: 'success', message: response.data.message });
                            } else {
                                this.$emitter.emit('add-flash', { type: 'warning', message: response.data.data.message });
                            }

                            this.isAddingToCart = false;
                        })
                        .catch(error => {
                            this.$emitter.emit('add-flash', { type: 'error', message: error.response.data.message });

                            if (error.response.data.redirect_uri) {
                                window.location.href = error.response.data.redirect_uri;
                            }
                            
                            this.isAddingToCart = false;
                        });
                },
                goToProduct(urlKey) {
                const baseUrl = '<?php echo e(route('shop.product_or_category.index', '')); ?>';
                    window.location.href = `${baseUrl}/${urlKey}`;
                },
            },

        });
    </script>
<?php $__env->stopPush(); endif; ?><?php /**PATH D:\venkat clone repo\geniusmart\mart\digitalmartgenius\packages\Webkul\Shop\src/resources/views/components/products/card.blade.php ENDPATH**/ ?>