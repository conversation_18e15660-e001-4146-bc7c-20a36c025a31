<?php

namespace Webkul\Shop\Http\Requests\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Webkul\Core\Rules\TenDigitPhone;

class VendorRegistrationRequest extends FormRequest
{
    /**
     * Define your rules.
     *
     * @var array
     */
    private $rules = [
        'company_name'                  => 'string|required|max:255',
        'company_email'                 => 'email|required|unique:vendors,company_email|max:255',
        'business_phone'                => 'required|string|max:20',
        'business_type'                 => 'nullable|string|in:sole_proprietorship,partnership,private_limited,public_limited,llp',
        'company_registration_number'   => 'nullable|string|max:255',
        'gst_number'                    => 'nullable|string|regex:/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/|max:15',
        'pan_number'                    => 'nullable|string|regex:/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/|max:10',
        'password'                      => 'required|min:6|confirmed',
        'terms_accepted'                => 'required|accepted',
    ];

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = $this->rules;

        // 10-digit phone validation (no alphabets, exactly 10 digits)
        $rules['business_phone'] = [
            'required',
            'string',
            'max:15', // Allow some spaces but limit total length
            new TenDigitPhone
        ];

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'company_name.required'                 => 'Company name is required.',
            'company_email.required'                => 'Company email is required.',
            'company_email.email'                   => 'Please enter a valid email address.',
            'company_email.unique'                  => 'This email is already registered as a vendor.',
            'business_phone.required'               => 'Business phone number is required.',
            'business_phone.max'                    => 'Business phone number is too long.',
            'business_type.in'                      => 'Please select a valid business type.',
            'gst_number.regex'                      => 'GST number format is invalid. Example: 22AAAAA0000A1Z5',
            'gst_number.max'                        => 'GST number cannot exceed 15 characters.',
            'pan_number.regex'                      => 'PAN number format is invalid. Example: **********',
            'pan_number.max'                        => 'PAN number cannot exceed 10 characters.',
            'password.required'                     => 'Password is required.',
            'password.min'                          => 'Password must be at least 6 characters long.',
            'password.confirmed'                    => 'Password confirmation does not match.',
            'terms_accepted.required'               => 'You must accept the terms and conditions.',
            'terms_accepted.accepted'               => 'You must accept the terms and conditions to proceed.',
        ];
    }
}
