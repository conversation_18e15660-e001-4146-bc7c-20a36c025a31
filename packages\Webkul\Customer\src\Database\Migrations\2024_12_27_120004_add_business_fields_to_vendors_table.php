<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
          
            $table->string('business_phone')->nullable()->after('company_email')->change();
            $table->enum('business_type', ['sole_proprietorship', 'partnership', 'private_limited', 'public_limited', 'llp'])->nullable()->after('business_phone')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {

            $table->dropColumn(['business_phone', 'business_type']);
        });
    }
};
