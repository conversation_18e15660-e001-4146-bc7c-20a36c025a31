<?php

namespace Webkul\Shop\Http\Controllers\Customer\Account;

use Webkul\Shop\Http\Controllers\Controller;
use Webkul\Admin\DataGrids\Enquiry\CustomerDataGrid;

class EnquiryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:customer');
    }

    /**
     * Display a listing of the customer's enquiries.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        if (request()->ajax()) {
            return datagrid(CustomerDataGrid::class)->process();
        }

        return view('shop::customers.account.enquiries.index');
    }

    /**
     * Display the specified enquiry.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function view($id)
    {
        // Get the enquiry and ensure it belongs to the current customer
        $enquiry = \DB::table('enquiries')
            ->leftJoin('products', 'enquiries.product', '=', 'products.id')
            ->leftJoin('admins', 'products.vendor', '=', 'admins.id')
            ->select(
                'enquiries.*',
                'products.name as product_name',
                'products.sku as product_sku',
                'products.id as product_id',
                'admins.name as vendor_name'
            )
            ->where('enquiries.id', $id)
            ->where('enquiries.user', auth()->guard('customer')->user()->id)
            ->first();

        if (!$enquiry) {
            abort(404, 'Enquiry not found or you do not have permission to view it.');
        }

        return view('shop::customers.account.enquiries.view', compact('enquiry'));
    }
}
