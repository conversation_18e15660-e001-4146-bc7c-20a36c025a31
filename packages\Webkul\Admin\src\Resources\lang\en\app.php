<?php

return [
    'users' => [
        'sessions' => [
            'email'                  => 'Email Address',
            'forget-password-link'   => 'Forget Password ?',
            'password'               => 'Password',
            'powered-by-description' => 'Powered by :bagisto, ',
            'submit-btn'             => 'Sign In',
            'title'                  => 'Sign In',
        ],

        'forget-password' => [
            'create' => [
                'email'                  => 'Registered Email',
                'email-not-exist'        => 'Email Not Exists',
                'page-title'             => 'Forget Password',
                'powered-by-description' => 'Powered by :bagisto, ',
                'reset-link-sent'        => 'Reset Password link sent',
                'sign-in-link'           => 'Back to Sign In ?',
                'submit-btn'             => 'Reset',
                'title'                  => 'Recover Password',
            ],
        ],

        'reset-password' => [
            'back-link-title'        => 'Back to Sign In ?',
            'confirm-password'       => 'Confirm Password',
            'email'                  => 'Registered Email',
            'password'               => 'Password',
            'powered-by-description' => 'Powered by  ',
            'submit-btn'             => 'Reset Password',
            'title'                  => 'Reset Password',
        ],
    ],

    'notifications' => [
        'description-text'      => 'List all the Notifications',
        'marked-success'        => 'All notifications have been marked as read',
        'no-record'             => 'No Record Found',
        'of'                    => 'of',
        'per-page'              => 'Per Page',
        'read-all'              => 'Mark as Read',
        'title'                 => 'Notifications',
        'view-all'              => 'View All',

        'order-status-messages' => [
            'all'             => 'All',
            'canceled'        => 'Order Canceled',
            'closed'          => 'Order Closed',
            'completed'       => 'Order Completed',
            'pending'         => 'Order Pending',
            'pending-payment' => 'Pending Payment',
            'processing'      => 'Order Processing',
        ],
    ],

    'account' => [
        'edit' => [
            'back-btn'          => 'Back',
            'change-password'   => 'Change Password',
            'confirm-password'  => 'Confirm Password',
            'current-password'  => 'Current Password',
            'email'             => 'Email',
            'general'           => 'General',
            'invalid-password'  => 'The current password you entered is incorrect.',
            'name'              => 'Name',
            'password'          => 'Password',
            'profile-image'     => 'Profile Image',
            'save-btn'          => 'Save Account',
            'title'             => 'My Account',
            'update-success'    => 'Account updated successfully',
            'upload-image-info' => 'Upload a Profile Image (110px X 110px) in PNG or JPG Format',
        ],
    ],

    'dashboard' => [
        'index' => [
            'add-customer'                => 'Add Customer',
            'add-product'                 => 'Add Product',
            'all-channels'                => 'All Channels',
            'attribute-code'              => 'Attribute Code',
            'average-sale'                => 'Average Order Sale',
            'color'                       => 'Color',
            'customer-info'               => 'No Customer Found With Most Sales',
            'customer-with-most-sales'    => 'Customer With Most Sales',
            'date-duration'               => ':start - :end',
            'decreased'                   => ':progress%',
            'empty-threshold'             => 'Empty Threshold',
            'empty-threshold-description' => 'There is no product available',
            'end-date'                    => 'End Date',
            'from'                        => 'From',
            'increased'                   => ':progress%',
            'more-products'               => ':product_count+ More Images',
            'order'                       => ':total_orders Orders',
            'order-count'                 => ':count Orders',
            'order-id'                    => '#:id',
            'overall-details'             => 'Overall Details',
            'pay-by'                      => 'Pay By - :method',
            'product-count'               => ':count Products',
            'product-image'               => 'Product Image',
            'product-info'                => 'Add related products on the go.',
            'product-number'              => 'Product - :product_number',
            'revenue'                     => 'Revenue :total',
            'sale-count'                  => ':count Sales',
            'sales'                       => 'Sales',
            'sku'                         => 'SKU - :sku',
            'start-date'                  => 'Start Date',
            'stock-threshold'             => 'Stock Threshold',
            'store-stats'                 => 'Store Stats',
            'title'                       => 'Dashboard',
            'to'                          => 'To',
            'today-customers'             => 'Today’s Customers',
            'today-details'               => 'Today’s Details',
            'today-orders'                => 'Today’s Orders',
            'today-sales'                 => 'Today’s Sales',
            'top-performing-categories'   => 'Top Performing Categories',
            'top-selling-products'        => 'Top Selling Products',
            'total-customers'             => 'Total Customers',
            'total-orders'                => 'Total Orders',
            'total-sales'                 => 'Total Sales',
            'total-stock'                 => ':total_stock Stock',
            'total-unpaid-invoices'       => 'Total Unpaid Invoices',
            'unique-visitors'             => ':count unique',
            'user-info'                   => 'Quickly Review what’s going on in your store',
            'user-name'                   => 'Hi ! :user_name',
            'visitors'                    => 'Visitors',
        ],
    ],

    'sales' => [
        'orders' => [
            'index' => [
                'create-btn' => 'Create Order',
                'title'      => 'Orders',

                'search-customer' => [
                    'create-btn'  => 'Create Customer',
                    'empty-info'  => 'No customers available for search term.',
                    'empty-title' => 'No customers found',
                    'search-by'   => 'Search by email or name',
                    'title'       => 'Select Customer',
                ],

                'datagrid' => [
                    'canceled'        => 'Canceled',
                    'channel-name'    => 'Channel',
                    'closed'          => 'Closed',
                    'completed'       => 'Completed',
                    'customer'        => 'Customer',
                    'date'            => 'Date',
                    'email'           => 'Email',
                    'fraud'           => 'Fraud',
                    'grand-total'     => 'Grand Total',
                    'id'              => '#:id',
                    'items'           => 'Items',
                    'location'        => 'Location',
                    'order-id'        => 'Order ID',
                    'pay-by'          => 'Pay By - :method',
                    'pay-via'         => 'Pay Via',
                    'pending-payment' => 'Pending Payment',
                    'pending'         => 'Pending',
                    'processing'      => 'Processing',
                    'product-count'   => ':count + More Products',
                    'status'          => 'Status',
                    'success'         => 'Success',
                    'view'            => 'View',
                ],
            ],

            'create' => [
                'add-to-cart'             => 'Add to Cart',
                'back-btn'                => 'Back',
                'check-billing-address'   => 'Billing address is missing.',
                'check-shipping-address'  => 'Shipping address is missing.',
                'configuration'           => 'Configuration',
                'coupon-already-applied'  => 'Coupon code already applied.',
                'coupon-applied'          => 'Coupon code applied successfully.',
                'coupon-error'            => 'Coupon code can\'t be applied.',
                'coupon-not-found'        => 'Coupon Not Found',
                'coupon-remove'           => 'Coupon code removed successfully.',
                'error'                   => 'Something went wrong',
                'minimum-order-error'     => 'The minimum order amount is not met.',
                'order-placed-success'    => 'Order placed successfully.',
                'payment-not-supported'   => 'This payment method is not supported.',
                'save-btn'                => 'Create Order',
                'specify-payment-method'  => 'Payment method is missing.',
                'specify-shipping-method' => 'Shipping method is missing.',
                'title'                   => 'Create Order for :name',

                'types' => [
                    'simple' => [
                        'none'         => 'None',
                        'total-amount' => 'Total Amount',
                    ],

                    'configurable' => [
                        'select-options' => 'Please select an option',
                    ],

                    'bundle' => [
                        'none'         => 'None',
                        'total-amount' => 'Total Amount',
                    ],

                    'grouped' => [
                        'name' => 'Name',
                    ],

                    'downloadable' => [
                        'title' => 'Links',
                    ],

                    'virtual' => [
                        'none'         => 'None',
                        'total-amount' => 'Total Amount',
                    ],
                ],

                'cart' => [
                    'success-add-to-cart' => 'Product added to cart successfully',
                    'success-remove'      => 'Item removed from cart successfully',
                    'success-update'      => 'Cart item updated successfully',

                    'items' => [
                        'add-product'       => 'Add Product',
                        'amount-per-unit'   => ':amount Per Unit x :qty Quantity',
                        'delete'            => 'Delete',
                        'empty-description' => 'No items found in your cart.',
                        'empty-title'       => 'Empty Cart Items',
                        'excl-tax'          => 'Excl. Tax',
                        'move-to-wishlist'  => 'Move to Wishlist',
                        'see-details'       => 'See Details',
                        'sku'               => 'SKU - :sku',
                        'sub-total'         => 'Sub Total - :sub_total',
                        'title'             => 'Cart Items',

                        'search' => [
                            'add-to-cart'   => 'Add To Cart',
                            'available-qty' => ':qty Available',
                            'empty-info'    => 'No products available for search term.',
                            'empty-title'   => 'No products found',
                            'product-image' => 'Product Image',
                            'qty'           => 'Qty',
                            'sku'           => 'SKU - :sku',
                            'title'         => 'Search Products',
                        ],
                    ],

                    'address' => [
                        'add-btn'          => 'Add Address',
                        'add-new'          => 'Add new address',
                        'add-new-address'  => 'Add new address',
                        'addresses'        => 'Addresses',
                        'back'             => 'Back',
                        'billing-address'  => 'Billing Address',
                        'city'             => 'City',
                        'company-name'     => 'Company Name',
                        'confirm'          => 'Confirm',
                        'country'          => 'Country',
                        'edit-btn'         => 'Edit Address',
                        'email'            => 'Email',
                        'first-name'       => 'First Name',
                        'last-name'        => 'Last Name',
                        'postcode'         => 'Zip/Postcode',
                        'proceed'          => 'Proceed',
                        'same-as-billing'  => 'Use same address for shipping?',
                        'save'             => 'Save',
                        'save-address'     => 'Save this to address book',
                        'select-country'   => 'Select Country',
                        'select-state'     => 'Select State',
                        'shipping-address' => 'Shipping Address',
                        'state'            => 'State',
                        'street-address'   => 'Street Address',
                        'telephone'        => 'Telephone',
                        'title'            => 'Address',
                        'vat-id'           => 'Vat ID',
                    ],

                    'payment' => [
                        'title' => 'Payment',
                    ],

                    'shipping' => [
                        'title' => 'Shipping',
                    ],

                    'summary' => [
                        'apply-coupon'             => 'Apply Coupon',
                        'discount-amount'          => 'Discount Amount',
                        'enter-your-code'          => 'Enter your code',
                        'grand-total'              => 'Grand Total',
                        'place-order'              => 'Place Order',
                        'processing'               => 'Processing',
                        'shipping-amount-excl-tax' => 'Shipping Amount (Excl. Tax)',
                        'shipping-amount-incl-tax' => 'Shipping Amount (Incl. Tax)',
                        'shipping-amount'          => 'Shipping Amount',
                        'sub-total-excl-tax'       => 'Subtotal (Excl. Tax)',
                        'sub-total-incl-tax'       => 'Subtotal (Incl. Tax)',
                        'sub-total'                => 'Subtotal',
                        'tax'                      => 'Tax',
                        'title'                    => 'Order Summary',
                    ],
                ],

                'cart-items' => [
                    'add-to-cart'       => 'Add to Cart',
                    'delete'            => 'Delete',
                    'empty-description' => 'No items found in your cart.',
                    'empty-title'       => 'Empty Cart',
                    'excl-tax'          => 'Excl. Tax: ',
                    'see-details'       => 'See Details',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Cart Items',
                ],

                'recent-order-items' => [
                    'add-to-cart'       => 'Add to Cart',
                    'empty-description' => 'No items found in your recent orders.',
                    'empty-title'       => 'Empty Orders',
                    'see-details'       => 'See Details',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Recent Order Items',
                    'view'              => 'View',
                ],

                'wishlist-items' => [
                    'add-to-cart'       => 'Add to Cart',
                    'delete'            => 'Delete',
                    'empty-description' => 'No items found in your wishlist.',
                    'empty-title'       => 'Empty Wishlist Items',
                    'see-details'       => 'See Details',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Wishlist Items',
                ],

                'compare-items' => [
                    'add-to-cart'       => 'Add to Cart',
                    'delete'            => 'Delete',
                    'empty-description' => 'No items found in your compare list.',
                    'empty-title'       => 'Empty Compare Items',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Compare Items',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount Per Unit x :qty Quantity',
                'billing-address'                => 'Billing Address',
                'cancel-msg'                     => 'Are your sure you want to cancel this order',
                'cancel-success'                 => 'Order cancelled successfully',
                'cancel'                         => 'Cancel',
                'canceled'                       => 'Canceled',
                'channel'                        => 'Channel',
                'closed'                         => 'Closed',
                'comment-success'                => 'Comment added successfully.',
                'comments'                       => 'Comments',
                'completed'                      => 'Completed',
                'contact'                        => 'Contact',
                'create-success'                 => 'Order created successfully',
                'currency'                       => 'Currency',
                'customer-group'                 => 'Customer Group',
                'customer-not-notified'          => ':date | Customer <b>Not Notified</b>',
                'customer-notified'              => ':date | Customer <b>Notified</b>',
                'customer'                       => 'Customer',
                'discount'                       => 'Discount - :discount',
                'download-pdf'                   => 'Download PDF',
                'fraud'                          => 'Fraud',
                'grand-total'                    => 'Grand Total - :grand_total',
                'invoice-id'                     => 'Invoice #:invoice',
                'invoices'                       => 'Invoices',
                'item-canceled'                  => 'Canceled (:qty_canceled)',
                'item-invoice'                   => 'Invoiced (:qty_invoiced)',
                'item-ordered'                   => 'Ordered (:qty_ordered)',
                'item-refunded'                  => 'Refunded (:qty_refunded)',
                'item-shipped'                   => 'Shipped (:qty_shipped)',
                'name'                           => 'Name',
                'no-invoice-found'               => 'No Invoice Found',
                'no-refund-found'                => 'No Refund Found',
                'no-shipment-found'              => 'No Shipments Found',
                'notify-customer'                => 'Notify Customer',
                'order-date'                     => 'Order Date',
                'order-information'              => 'Order Information',
                'order-status'                   => 'Order Status',
                'payment-and-shipping'           => 'Payment and Shipping',
                'payment-method'                 => 'Payment method',
                'pending_payment'                => 'Pending Payment',
                'pending'                        => 'Pending',
                'per-unit'                       => 'Per Unit',
                'price-incl-tax'                 => 'Price (Incl. Tax) - :price',
                'price-excl-tax'                 => 'Price (Excl. Tax) - :price',
                'price'                          => 'Price - :price',
                'processing'                     => 'Processing',
                'quantity'                       => 'Quantity',
                'refund-id'                      => 'Refund #:refund',
                'refund'                         => 'Refund',
                'refunded'                       => 'Refunded',
                'reorder'                        => 'Reorder',
                'ship'                           => 'Ship',
                'shipment'                       => 'Shipment #:shipment',
                'shipments'                      => 'Shipments',
                'shipping-address'               => 'Shipping Address',
                'shipping-and-handling-incl-tax' => 'Shipping and Handling (Incl. Tax)',
                'shipping-and-handling-excl-tax' => 'Shipping and Handling (Excl. Tax)',
                'shipping-and-handling'          => 'Shipping and Handling',
                'shipping-method'                => 'Shipping Method',
                'shipping-price'                 => 'Shipping Price',
                'sku'                            => 'SKU - :sku',
                'status'                         => 'Status',
                'sub-total-incl-tax'             => 'Sub Total (Incl. Tax) - :sub_total',
                'sub-total-excl-tax'             => 'Sub Total (Excl. Tax) - :sub_total',
                'sub-total'                      => 'Sub Total - :sub_total',
                'submit-comment'                 => 'Submit Comment',
                'summary-grand-total'            => 'Grand Total',
                'summary-sub-total-incl-tax'     => 'Sub Total (Incl. Tax)',
                'summary-sub-total-excl-tax'     => 'Sub Total (Excl. Tax)',
                'summary-sub-total'              => 'Sub Total',
                'summary-discount'               => 'Discount',
                'summary-tax'                    => 'Tax',
                'tax'                            => 'Tax (:percent) - :tax',
                'title'                          => 'Order #:order_id',
                'total-due'                      => 'Total Due',
                'total-paid'                     => 'Total Paid',
                'total-refund'                   => 'Total Refund',
                'view'                           => 'View',
                'write-your-comment'             => 'Write your comment',
            ],
        ],

        'shipments' => [
            'index' => [
                'title' => 'Shipments',

                'datagrid' => [
                    'id'               => 'ID',
                    'inventory-source' => 'Inventory Source',
                    'order-date'       => 'Order Date',
                    'order-id'         => 'Order ID',
                    'shipment-date'    => 'Shipment Date',
                    'shipment-to'      => 'Shipment To',
                    'total-qty'        => 'Total Quantity',
                    'view'             => 'View',
                ],
            ],

            'create' => [
                'amount-per-unit'  => ':amount Per Unit x :qty Quantity',
                'cancel-error'     => 'Order not be canceled',
                'carrier-name'     => 'Carrier Name',
                'create-btn'       => 'Create Shipment',
                'creation-error'   => 'Error on Shipment creation',
                'item-canceled'    => 'Canceled (:qty_canceled)',
                'item-invoice'     => 'Invoiced (:qty_invoiced)',
                'item-ordered'     => 'Ordered (:qty_ordered)',
                'item-refunded'    => 'Refunded (:qty_refunded)',
                'item-shipped'     => 'Shipped (:qty_shipped)',
                'order-error'      => 'Shipment is not valid',
                'per-unit'         => 'Per Unit',
                'qty-available'    => 'Qty. Available',
                'qty-to-ship'      => 'Qty. To Ship',
                'quantity-invalid' => 'Qty. Invalid',
                'sku'              => 'SKU - :sku',
                'source'           => 'Source',
                'success'          => 'Shipment created successfully',
                'title'            => 'Create new Shipment',
                'tracking-number'  => 'Tracking Number',
            ],

            'view' => [
                'billing-address'      => 'Billing Address',
                'carrier-title'        => 'Carrier Title',
                'channel'              => 'Channel',
                'currency'             => 'Currency',
                'customer'             => 'Customer',
                'email'                => 'Email - :email',
                'inventory-source'     => 'Inventory Source',
                'order-date'           => 'Order Date',
                'order-id'             => 'Order ID',
                'order-information'    => 'Order Information',
                'order-status'         => 'Order Status',
                'ordered-items'        => 'Ordered Items',
                'payment-and-shipping' => 'Payment and Shipping',
                'payment-method'       => 'Payment Method',
                'product-image'        => 'Product Image',
                'qty'                  => 'Quantity - :qty',
                'shipping-address'     => 'Shipping Address',
                'shipping-method'      => 'Shipping Method',
                'shipping-price'       => 'Shipping Price',
                'sku'                  => 'SKU - :sku ',
                'title'                => 'Shipment #:shipment_id',
                'tracking-number'      => 'Tracking Number',
            ],
        ],

        'refunds' => [
            'index' => [
                'title' => 'Refunds',

                'datagrid' => [
                    'billed-to'       => 'Billed To',
                    'id'              => 'ID',
                    'order-id'        => 'Order ID',
                    'refund-date'     => 'Refund Date',
                    'refunded-amount' => 'Refunded Amount',
                    'view'            => 'View',
                ],
            ],

            'view' => [
                'account-information'        => 'Account Information',
                'adjustment-fee'             => 'Adjustment Fee',
                'adjustment-refund'          => 'Adjustment Refund',
                'base-discounted-amount'     => 'Discounted Amount - :base_discounted_amount',
                'billing-address'            => 'Billing Address',
                'currency'                   => 'Currency',
                'sub-total-amount-excl-tax'  => 'Sub Total (Excl. Tax) - :discounted_amount',
                'sub-total-amount-incl-tax'  => 'Sub Total (Incl. Tax) - :discounted_amount',
                'sub-total-amount'           => 'Sub Total - :discounted_amount',
                'grand-total'                => 'Grand Total',
                'order-channel'              => 'Order Channel',
                'order-date'                 => 'Order Date',
                'order-id'                   => 'Order Id',
                'order-information'          => 'Order Information',
                'order-status'               => 'Order status',
                'payment-information'        => 'Payment Information',
                'payment-method'             => 'Payment Method',
                'price-excl-tax'             => 'Price (Excl. Tax) - :price',
                'price-incl-tax'             => 'Price (Incl. Tax) - :price',
                'price'                      => 'Price - :price',
                'product-image'              => 'Product Image',
                'product-ordered'            => 'Products Ordered',
                'qty'                        => 'QTY - :qty',
                'refund'                     => 'Refund',
                'shipping-address'           => 'Shipping Address',
                'shipping-handling-excl-tax' => 'Shipping & Handling (Excl. Tax)',
                'shipping-handling-incl-tax' => 'Shipping & Handling (Incl. Tax)',
                'shipping-handling'          => 'Shipping & Handling',
                'shipping-method'            => 'Shipping Method',
                'shipping-price'             => 'Shipping Price',
                'sku'                        => 'SKU - :sku',
                'sub-total-excl-tax'         => 'Sub Total (Excl. Tax)',
                'sub-total-incl-tax'         => 'Sub Total (Incl. Tax)',
                'sub-total'                  => 'Sub Total',
                'tax'                        => 'Tax',
                'tax-amount'                 => 'Tax Amount - :tax_amount',
                'title'                      => 'Refund #:refund_id',
            ],

            'create' => [
                'adjustment-fee'              => 'Adjustment Fee',
                'adjustment-refund'           => 'Adjustment Refund',
                'amount-per-unit'             => ':amount Per Unit x :qty Quantity',
                'create-success'              => 'Refund created successfully',
                'creation-error'              => 'Refund creation is not allowed.',
                'discount-amount'             => 'Discount Amount',
                'grand-total'                 => 'Grand Total',
                'invalid-qty'                 => 'We found an invalid quantity to invoice items.',
                'invalid-refund-amount-error' => 'Refund amount should be non zero.',
                'item-canceled'               => 'Canceled (:qty_canceled)',
                'item-invoice'                => 'Invoiced (:qty_invoiced)',
                'item-ordered'                => 'Ordered (:qty_ordered)',
                'item-refunded'               => 'Refunded (:qty_refunded)',
                'item-shipped'                => 'Shipped (:qty_shipped)',
                'per-unit'                    => 'Per Unit',
                'price'                       => 'Price',
                'qty-to-refund'               => 'Qty To Refund',
                'refund-btn'                  => 'Refund',
                'refund-limit-error'          => 'Refund Amount :amount can not proceed.',
                'refund-shipping'             => 'Refund Shipping',
                'sku'                         => 'SKU - :sku',
                'subtotal'                    => 'Subtotal',
                'tax-amount'                  => 'Tax Amount',
                'title'                       => 'Create Refund',
                'update-totals-btn'           => 'Update Totals',
            ],
        ],

        'invoices' => [
            'index' => [
                'title' => 'Invoices',

                'datagrid' => [
                    'action'       => 'Actions',
                    'grand-total'  => 'Grand Total',
                    'id'           => 'ID',
                    'invoice-date' => 'Invoice Date',
                    'order-id'     => 'Order ID',
                    'overdue'      => 'Overdue',
                    'paid'         => 'Paid',
                    'pending'      => 'Pending',
                    'status'       => 'Status',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount Per Unit x :qty Quantity',
                'channel'                        => 'Channel',
                'customer-email'                 => 'Email - :email',
                'customer'                       => 'Customer',
                'discount'                       => 'Discount Amount - :discount',
                'email'                          => 'Email',
                'grand-total'                    => 'Grand Total',
                'invoice-items'                  => 'Invoice Items',
                'invoice-sent'                   => 'Invoice sent successfully',
                'invoice-status'                 => 'Invoice Status',
                'order-date'                     => 'Order Date',
                'order-id'                       => 'Order ID',
                'order-information'              => 'Order Information',
                'order-status'                   => 'Order Status',
                'price-excl-tax'                 => 'Price (Excl. Tax) - :price',
                'price-incl-tax'                 => 'Price (Incl. Tax) - :price',
                'price'                          => 'Price - :price',
                'print'                          => 'Print',
                'product-image'                  => 'Product Image',
                'qty'                            => 'Quantity - :qty',
                'send-btn'                       => 'Send',
                'send-duplicate-invoice'         => 'Send Duplicate Invoice',
                'send'                           => 'Send',
                'shipping-and-handling-excl-tax' => 'Shipping and Handling (Excl. Tax)',
                'shipping-and-handling-incl-tax' => 'Shipping and Handling (Incl. Tax)',
                'shipping-and-handling'          => 'Shipping and Handling',
                'sku'                            => 'SKU - :sku',
                'sub-total-excl-tax'             => 'Sub Total (Excl. Tax) - :sub_total',
                'sub-total-incl-tax'             => 'Sub Total (Incl. Tax) - :sub_total',
                'sub-total-summary-excl-tax'     => 'Sub Total (Excl. Tax)',
                'sub-total-summary-incl-tax'     => 'Sub Total (Incl. Tax)',
                'sub-total-summary'              => 'Sub Total',
                'sub-total'                      => 'Sub Total - :sub_total',
                'summary-discount'               => 'Discount Amount',
                'summary-tax'                    => 'Tax Amount',
                'tax'                            => 'Tax Amount - :tax',
                'title'                          => 'Invoice #:invoice_id',
            ],

            'create'   => [
                'amount-per-unit'    => ':amount Per Unit x :qty Quantity',
                'create-invoice'     => 'Create Invoice',
                'create-success'     => 'Invoice created successfully',
                'create-transaction' => 'Create Transaction',
                'creation-error'     => 'Order invoice creation is not allowed.',
                'invalid-qty'        => 'We found an invalid quantity to invoice items.',
                'invoice'            => 'Invoice',
                'new-invoice'        => 'New Invoice',
                'product-error'      => 'Invoice can not be created without products.',
                'product-image'      => 'Product Image',
                'qty-to-invoiced'    => 'Qty to invoiced',
                'sku'                => 'SKU - :sku',
            ],

            'invoice-pdf' => [
                'bank-details'               => 'Bank Details',
                'bill-to'                    => 'Bill to',
                'contact-number'             => 'Contact Number',
                'contact'                    => 'Contact',
                'date'                       => 'Invoice Date',
                'discount'                   => 'Discount',
                'excl-tax'                   => 'Excl. Tax:',
                'grand-total'                => 'Grand Total',
                'invoice-id'                 => 'Invoice ID',
                'invoice'                    => 'Invoice',
                'order-date'                 => 'Order Date',
                'order-id'                   => 'Order ID',
                'payment-method'             => 'Payment Method',
                'payment-terms'              => 'Payment Terms',
                'price'                      => 'Price',
                'product-name'               => 'Product Name',
                'qty'                        => 'Quantity',
                'ship-to'                    => 'Ship to',
                'shipping-handling-excl-tax' => 'Shipping Handling (Excl. Tax)',
                'shipping-handling-incl-tax' => 'Shipping Handling (Incl. Tax)',
                'shipping-handling'          => 'Shipping Handling',
                'shipping-method'            => 'Shipping Method',
                'sku'                        => 'SKU',
                'subtotal-excl-tax'          => 'Subtotal (Excl. Tax)',
                'subtotal-incl-tax'          => 'Subtotal (Incl. Tax)',
                'subtotal'                   => 'Subtotal',
                'tax-amount'                 => 'Tax Amount',
                'tax'                        => 'Tax',
                'vat-number'                 => 'Vat Number',
            ],
        ],

        'invoice-transaction' => [
            'id'               => 'ID',
            'transaction-date' => 'Transaction Date',
            'transaction-id'   => 'Transaction ID',
            'view'             => 'View',
        ],

        'transactions' => [
            'index' => [
                'create-btn' => 'Create Transactions',
                'title'      => 'Transactions',

                'datagrid' => [
                    'completed'          => 'Completed',
                    'id'                 => 'ID',
                    'invoice-id'         => 'Invoice ID',
                    'order-id'           => 'Order ID',
                    'paid'               => 'Paid',
                    'pending'            => 'Pending',
                    'status'             => 'Status',
                    'transaction-amount' => 'Amount',
                    'transaction-date'   => 'Date',
                    'transaction-id'     => 'Transaction ID',
                    'view'               => 'View',
                ],

                'create' => [
                    'already-paid'               => 'Already paid',
                    'amount'                     => 'Amount',
                    'create-transaction'         => 'Create Transaction',
                    'invoice-id'                 => 'Invoice ID',
                    'invoice-missing'            => 'Invoice Missing',
                    'payment-method'             => 'Payment Method',
                    'save-transaction'           => 'Save Transaction',
                    'transaction-amount-exceeds' => 'Transaction Amount exceeds',
                    'transaction-amount-zero'    => 'Transaction Amount zero',
                    'transaction-saved'          => 'Transaction saved successfully.',
                ],

                'view' => [
                    'amount'           => 'Amount',
                    'created-at'       => 'Created At',
                    'invoice-id'       => 'Invoice ID',
                    'order-id'         => 'Order ID',
                    'payment-details'  => 'Payment Details',
                    'payment-method'   => 'Payment Method',
                    'status'           => 'Status',
                    'title'            => 'Transaction Details',
                    'transaction-id'   => 'Transaction ID ',
                ],
            ],
        ],

        'booking' => [
            'index' => [
                'datagrid' => [
                    'created-date' => 'Created Date',
                    'from'         => 'From',
                    'id'           => 'ID',
                    'order-id'     => 'Order ID',
                    'qty'          => 'QTY',
                    'to'           => 'To',
                    'view'         => 'View',
                ],

                'title'    => 'Bookings',
            ],

            'calendar' => [
                'booking-date'     => 'Booking Date',
                'booking-details'  => 'Booking Details',
                'canceled'         => 'Canceled',
                'closed'           => 'Closed',
                'done'             => 'Done',
                'order-id'         => 'Order Id',
                'pending'          => 'Pending',
                'price'            => 'Price',
                'status'           => 'Status',
                'time-slot'        => 'Time Slot:',
                'view-details'     => 'View Details',
            ],

            'title' => 'Bookings Product',
        ],
    ],

    'catalog' => [
        'products' => [
            'index' => [
                'already-taken' => 'The :name has already been taken.',
                'create-btn'    => 'Create Product',
                'title'         => 'Products',

                'create' => [
                    'back-btn'                => 'Back',
                    'configurable-attributes' => 'Configurable Attributes',
                    'create-btn'              => 'Create Product',
                    'family'                  => 'Family',
                    'save-btn'                => 'Save Product',
                    'sku'                     => 'SKU',
                    'title'                   => 'Create New Product',
                    'type'                    => 'Type',
                ],

                'datagrid' => [
                    'active'                 => 'Active',
                    'attribute-family-value' => 'Attribute Family - :attribute_family',
                    'attribute-family'       => 'Attribute Family',
                    'category'               => 'Category',
                    'channel'                => 'Channel',
                    'copy-of-slug'           => 'copy-of-:value',
                    'copy-of'                => 'Copy Of :value',
                    'delete'                 => 'Delete',
                    'disable'                => 'Disable',
                    'id-value'               => 'ID - :id',
                    'id'                     => 'ID',
                    'image'                  => 'Image',
                    'mass-delete-success'    => 'Selected Products Deleted Successfully',
                    'mass-update-success'    => 'Selected Products Updated Successfully',
                    'name'                   => 'Name',
                    'out-of-stock'           => 'Out of Stock',
                    'price'                  => 'Price',
                    'product-image'          => 'Product Image',
                    'qty-value'              => ':qty Available',
                    'qty'                    => 'Quantity',
                    'sku-value'              => 'SKU - :sku',
                    'sku'                    => 'SKU',
                    'status'                 => 'Status',
                    'type'                   => 'Type',
                    'update-status'          => 'Update Status',
                ],
            ],

            'edit' => [
                'preview'  => 'Preview',
                'remove'   => 'Remove',
                'save-btn' => 'Save Product',
                'title'    => 'Edit Product',

                'channels' => [
                    'title' => 'Channels',
                ],

                'price' => [
                    'group' => [
                        'add-group-price'           => 'Add Group Price',
                        'all-groups'                => 'All Groups',
                        'create-btn'                => 'Add New',
                        'discount-group-price-info' => 'For :qty Qty at discount of :price',
                        'edit-btn'                  => 'Edit',
                        'empty-info'                => 'Special pricing for customers belonging to a specific group.',
                        'fixed-group-price-info'    => 'For :qty Qty at fixed price of :price',
                        'title'                     => 'Customer Group Price',

                        'create' => [
                            'all-groups'     => 'All Groups',
                            'create-title'   => 'Create Customer Group Price',
                            'customer-group' => 'Customer Group',
                            'delete-btn'     => 'Delete',
                            'discount'       => 'Discount',
                            'fixed'          => 'Fixed',
                            'price'          => 'Price',
                            'price-type'     => 'Price Type',
                            'qty'            => 'Minimum Qty',
                            'save-btn'       => 'Save',
                            'update-title'   => 'Update Customer Group Price',
                        ],
                    ],
                ],

                'inventories' => [
                    'pending-ordered-qty'      => 'Pending Ordered Qty: :qty',
                    'pending-ordered-qty-info' => 'Pending Ordered quantity will be deducted from the respective inventory source after the shipment. In case of cancellation pending quantity will be available for sale.',
                    'title'                    => 'Inventories',
                ],

                'categories' => [
                    'title' => 'Categories',
                ],

                'images' => [
                    'info'  => 'Image resolution should be like 560px X 609px',
                    'title' => 'Images',
                ],

                'videos' => [
                    'error' => 'The :attribute may not be greater than :max kilobytes. Please choose a smaller file.',
                    'info'  => 'Maximum video size should be like :size',
                    'title' => 'Videos',
                ],

                'links' => [
                    'related-products' => [
                        'empty-info' => 'Add related products on the go.',
                        'info'       => 'In addition to the product the customer is viewing, they are presented with related products.',
                        'title'      => 'Related Products',
                    ],

                    'up-sells' => [
                        'empty-info' => 'Add up sells products on the go.',
                        'info'       => 'The customer is presented with an up-sell products, which serves as a premium or higher-quality alternative to the product they are currently viewing.',
                        'title'      => 'Up-Sell Products',
                    ],

                    'cross-sells' => [
                        'empty-info' => 'Add cross sells products on the go.',
                        'info'       => 'Adjacent to the shopping cart, you\'ll find these \"impulse-buy\" products positioned as cross-sells to complement the items already added to your cart.',
                        'title'      => 'Cross-Sell Products',
                    ],

                    'add-btn'           => 'Add Product',
                    'delete'            => 'Delete',
                    'empty-info'        => 'To add :type products on a go.',
                    'empty-title'       => 'Add Product',
                    'image-placeholder' => 'Product Image',
                    'sku'               => 'SKU - :sku',
                ],

                'types' => [
                    'simple' => [
                        'customizable-options' => [
                            'add-btn'           => 'Add Option',
                            'empty-info'        => 'To create customizable options on a go.',
                            'empty-title'       => 'Add Option',
                            'info'              => 'This will customize the simple product.',
                            'title'             => 'Customizable Item',

                            'update-create' => [
                                'is-required'               => 'Is Required',
                                'max-characters'            => 'Max Characters',
                                'name'                      => 'Title',
                                'no'                        => 'No',
                                'price'                     => 'Price',
                                'save-btn'                  => 'Save',
                                'supported-file-extensions' => 'Supported File Extensions',
                                'title'                     => 'Option',
                                'type'                      => 'Type',
                                'yes'                       => 'Yes',
                            ],

                            'option' => [
                                'add-btn'     => 'Add Option',
                                'delete'      => 'Delete',
                                'delete-btn'  => 'Delete',
                                'edit-btn'    => 'Edit',
                                'empty-info'  => 'To create various combination of product on a go.',
                                'empty-title' => 'Add Option',

                                'types' => [
                                    'text' => [
                                        'title' => 'Text',
                                    ],

                                    'textarea' => [
                                        'title' => 'Textarea',
                                    ],

                                    'checkbox' => [
                                        'title' => 'Checkbox',
                                    ],

                                    'radio' => [
                                        'title' => 'Radio',
                                    ],

                                    'select' => [
                                        'title' => 'Select',
                                    ],

                                    'multiselect' => [
                                        'title' => 'Multiselect',
                                    ],

                                    'date' => [
                                        'title' => 'Date',
                                    ],

                                    'datetime' => [
                                        'title' => 'Datetime',
                                    ],

                                    'time' => [
                                        'title' => 'Time',
                                    ],

                                    'file' => [
                                        'title' => 'File',
                                    ],
                                ],

                                'items' => [
                                    'update-create' => [
                                        'label'    => 'Label',
                                        'price'    => 'Price',
                                        'save-btn' => 'Save',
                                        'title'    => 'Option',
                                    ],
                                ],
                            ],

                            'validations' => [
                                'associated-product' => 'The product is already associated with a configurable, grouped, or bundle product.',
                            ],
                        ],
                    ],

                    'configurable' => [
                        'add-btn'           => 'Add Variant',
                        'delete-btn'        => 'Delete',
                        'edit-btn'          => 'Edit',
                        'empty-info'        => 'To create various combination of product on a go.',
                        'empty-title'       => 'Add Variant',
                        'image-placeholder' => 'Product Image',
                        'info'              => 'Variation products are depend on all possible combination of attribute.',
                        'qty'               => ':qty Qty',
                        'sku'               => 'SKU - :sku',
                        'title'             => 'Variations',

                        'create' => [
                            'description'            => 'Description',
                            'name'                   => 'Name',
                            'save-btn'               => 'Add',
                            'title'                  => 'Add Variant',
                            'variant-already-exists' => 'This variant already exists',
                        ],

                        'edit' => [
                            'disabled'        => 'Disabled',
                            'edit-info'       => 'If you want to update product information in detail, then go to the',
                            'edit-link-title' => 'Product Details Page',
                            'enabled'         => 'Enabled',
                            'images'          => 'Images',
                            'name'            => 'Name',
                            'price'           => 'Price',
                            'quantities'      => 'Quantities',
                            'save-btn'        => 'Save',
                            'sku'             => 'SKU',
                            'status'          => 'Status',
                            'title'           => 'Product',
                            'weight'          => 'Weight',
                        ],

                        'mass-edit' => [
                            'add-images'          => 'Add Images',
                            'apply-to-all-btn'    => 'Apply to All',
                            'apply-to-all-name'   => 'Apply a name to all variants.',
                            'apply-to-all-sku'    => 'Apply a price to all SKU.',
                            'apply-to-all-status' => 'Apply a status to all variants.',
                            'apply-to-all-weight' => 'Apply a weight to all variants.',
                            'edit-inventories'    => 'Edit Inventories',
                            'edit-names'          => 'Edit Names',
                            'edit-prices'         => 'Edit Prices',
                            'edit-sku'            => 'Edit SKU',
                            'edit-status'         => 'Edit Status',
                            'edit-weight'         => 'Edit Weight',
                            'name'                => 'Name',
                            'price'               => 'Price',
                            'remove-images'       => 'Remove Images',
                            'remove-variants'     => 'Remove Variants',
                            'select-action'       => 'Select Action',
                            'select-variants'     => 'Select Variants',
                            'status'              => 'Status',
                            'variant-name'        => 'Variant Name',
                            'variant-sku'         => 'Variant SKU',
                            'weight'              => 'Weight',
                        ],
                    ],

                    'grouped' => [
                        'add-btn'           => 'Add Product',
                        'default-qty'       => 'Default Qty',
                        'delete'            => 'Delete',
                        'empty-info'        => 'To create various combination of product on a go.',
                        'empty-title'       => 'Add Product',
                        'image-placeholder' => 'Product Image',
                        'info'              => 'A grouped product comprises standalone items presented as a set, allowing variations or coordination by season or theme. Each product can be bought individually or as part of the group.',
                        'sku'               => 'SKU - :sku',
                        'title'             => 'Group Products',
                    ],

                    'bundle' => [
                        'add-btn'           => 'Add Option',
                        'empty-info'        => 'To create bundle options on a go.',
                        'empty-title'       => 'Add Option',
                        'image-placeholder' => 'Product Image',
                        'info'              => 'A bundle product is a package of multiple items or services sold together at a special price, providing value and convenience to customers.',
                        'title'             => 'Bundle Items',

                        'update-create' => [
                            'checkbox'    => 'Checkbox',
                            'is-required' => 'Is Required',
                            'multiselect' => 'Multiselect',
                            'name'        => 'Title',
                            'no'          => 'No',
                            'radio'       => 'Radio',
                            'save-btn'    => 'Save',
                            'select'      => 'Select',
                            'title'       => 'Option',
                            'type'        => 'Type',
                            'yes'         => 'Yes',
                        ],

                        'option' => [
                            'add-btn'     => 'Add Product',
                            'default-qty' => 'Default Qty',
                            'delete'      => 'Delete',
                            'delete-btn'  => 'Delete',
                            'edit-btn'    => 'Edit',
                            'empty-info'  => 'To create various combination of product on a go.',
                            'empty-title' => 'Add Product',
                            'sku'         => 'SKU - :sku',

                            'types' => [
                                'checkbox' => [
                                    'info'  => 'Set default product using checkbox',
                                    'title' => 'Checkbox',
                                ],

                                'multiselect' => [
                                    'info'  => 'Set default product using checkbox button',
                                    'title' => 'Multiselect',
                                ],

                                'radio' => [
                                    'info'  => 'Set default product using radio button',
                                    'title' => 'Radio',
                                ],

                                'select' => [
                                    'info'  => 'Set default product using radio button',
                                    'title' => 'Select',
                                ],
                            ],
                        ],
                    ],

                    'booking' => [
                        'available-from' => 'Available From',
                        'available-to'   => 'Available To',
                        'location'       => 'Location',
                        'qty'            => 'Qty',
                        'title'          => 'Booking Type',

                        'available-every-week' => [
                            'no'    => 'No',
                            'title' => 'Available Every Week',
                            'yes'   => 'Yes',
                        ],

                        'appointment' => [
                            'break-duration' => 'Break Time b/w Slots (Mins)',
                            'slot-duration'  => 'Slot Duration (Mins)',

                            'same-slot-for-all-days' => [
                                'no'    => 'No',
                                'title' => 'Same Slot For All days',
                                'yes'   => 'Yes',
                            ],
                        ],

                        'default' => [
                            'add'              => 'Add',
                            'break-duration'   => 'Break Time b/w Slots (Mins)',
                            'close'            => 'Close',
                            'description'      => 'Booking Information',
                            'description-info' => 'According to slots the time duration will be created and visible. And The time duration will be created and displayed according to the slots. It will be unique across all slots and visible on the storefront.',
                            'edit'             => 'Edit',
                            'many'             => 'Many Bookings For One Days',
                            'one'              => 'One Booking For Many Days',
                            'open'             => 'Open',
                            'slot-add'         => 'Add Slots',
                            'slot-duration'    => 'Slot Duration (Mins)',
                            'slot-title'       => 'Slots Time Duration',
                            'title'            => 'Default',
                            'unavailable'      => 'Unavailable',

                            'modal' => [
                                'slot' => [
                                    'add-title'  => 'Add Slots',
                                    'close'      => 'Close',
                                    'day'        => 'Day',
                                    'edit-title' => 'Edit Slots',
                                    'friday'     => 'Friday',
                                    'from'       => 'From',
                                    'from-day'   => 'From Day',
                                    'from-time'  => 'From Time',
                                    'monday'     => 'Monday',
                                    'open'       => 'Open',
                                    'saturday'   => 'Saturday',
                                    'save'       => 'Save',
                                    'select'     => 'Select',
                                    'status'     => 'Status',
                                    'sunday'     => 'Sunday',
                                    'thursday'   => 'Thursday',
                                    'to'         => 'To',
                                    'to-day'     => 'To Day',
                                    'to-time'    => 'To Time',
                                    'tuesday'    => 'Tuesday',
                                    'wednesday'  => 'Wednesday',
                                    'week'       => ':day',
                                ],
                            ],
                        ],

                        'event' => [
                            'add'                => 'Add Tickets',
                            'delete'             => 'Delete',
                            'description'        => 'Description',
                            'description-info'   => 'There is no tickets available.',
                            'edit'               => 'Edit',
                            'name'               => 'Name',
                            'price'              => 'Price',
                            'qty'                => 'Quantity',
                            'special-price'      => 'Special Price',
                            'special-price-from' => 'Special Price From',
                            'special-price-to'   => 'Special Price To',
                            'title'              => 'Tickets',
                            'valid-from'         => 'Valid From',
                            'valid-until'        => 'Valid Until',

                            'modal' => [
                                'edit' => 'Edit Tickets',
                                'save' => 'Save',
                            ],
                        ],

                        'empty-info' => [
                            'tickets' => [
                                'add' => 'Add Tickets',
                            ],

                            'slots'   => [
                                'add'         => 'Add Slots',
                                'description' => 'Available Slots with time Duration.',
                            ],
                        ],

                        'rental' => [
                            'daily'        => 'Daily Basis',
                            'daily-hourly' => 'Both (Daily and Hourly Basis)',
                            'daily-price'  => 'Daily Price',
                            'hourly'       => 'Hourly Basis',
                            'hourly-price' => 'Hourly Price',
                            'title'        => 'Renting Type',

                            'same-slot-for-all-days' => [
                                'no'    => 'No',
                                'title' => 'Same Slot For All days',
                                'yes'   => 'Yes',
                            ],
                        ],

                        'slots' => [
                            'add'              => 'Add Slots',
                            'description-info' => 'According to slots the time duration will be created and visible. And The time duration will be created and displayed according to the slots. It will be unique across all slots and visible on the storefront.',
                            'save'             => 'Save',
                            'title'            => 'Slots Time Duration',
                            'unavailable'      => 'Unavailable',

                            'action' => [
                                'add' => 'Add',
                            ],

                            'modal' => [
                                'slot' => [
                                    'friday'    => 'Friday',
                                    'from'      => 'From',
                                    'monday'    => 'Monday',
                                    'saturday'  => 'Saturday',
                                    'sunday'    => 'Sunday',
                                    'thursday'  => 'Thursday',
                                    'to'        => 'To',
                                    'tuesday'   => 'Tuesday',
                                    'wednesday' => 'Wednesday',
                                ],
                            ],
                        ],

                        'table' => [
                            'break-duration'            => 'Break Time b/w Slots (Mins)',
                            'guest-capacity'            => 'Guest Capacity',
                            'guest-limit'               => 'Guest Limit Per Table',
                            'prevent-scheduling-before' => 'Prevent Scheduling Before',
                            'slot-duration'             => 'Slot Duration (Mins)',

                            'charged-per'               => [
                                'guest'  => 'Guest',
                                'table'  => 'Table',
                                'title'  => 'Charged Per',
                            ],

                            'same-slot-for-all-days'    => [
                                'no'    => 'No',
                                'title' => 'Same Slot For All days',
                                'yes'   => 'Yes',
                            ],
                        ],

                        'type' => [
                            'appointment' => 'Appointment Booking',
                            'default'     => 'Default Booking',
                            'event'       => 'Event Booking',
                            'many'        => 'Many',
                            'one'         => 'One',
                            'rental'      => 'Rental Booking',
                            'table'       => 'Table Booking',
                            'title'       => 'Type',
                        ],
                    ],

                    'downloadable' => [
                        'links' => [
                            'add-btn'     => 'Add Link',
                            'delete-btn'  => 'Delete',
                            'edit-btn'    => 'Edit',
                            'empty-info'  => 'To create link on a go.',
                            'empty-title' => 'Add Link',
                            'file'        => 'File : ',
                            'info'        => 'Downloadable product type allows to sell digital products, such as eBooks, software applications, music, games, etc.',
                            'sample-file' => 'Sample File : ',
                            'sample-url'  => 'Sample URL : ',
                            'title'       => 'Downloadable Links',
                            'url'         => 'URL : ',

                            'update-create' => [
                                'downloads'   => 'Download Allowed',
                                'file'        => 'File',
                                'file-type'   => 'File Type',
                                'name'        => 'Title',
                                'price'       => 'Price',
                                'sample'      => 'Sample',
                                'sample-type' => 'Sample Type',
                                'save-btn'    => 'Save',
                                'title'       => 'Link',
                                'url'         => 'URL',
                            ],
                        ],

                        'samples' => [
                            'add-btn'     => 'Add Sample',
                            'delete-btn'  => 'Delete',
                            'edit-btn'    => 'Edit',
                            'empty-info'  => 'To create sample on a go.',
                            'empty-title' => 'Add Sample',
                            'file'        => 'File : ',
                            'info'        => 'Downloadable product type allows to sell digital products, such as eBooks, software applications, music, games, etc.',
                            'title'       => 'Downloadable Samples',
                            'url'         => 'URL : ',

                            'update-create' => [
                                'file'      => 'File',
                                'file-type' => 'File Type',
                                'name'      => 'Title',
                                'save-btn'  => 'Save',
                                'title'     => 'Link',
                                'url'       => 'URL',
                            ],
                        ],
                    ],
                ],
            ],

            'create-success'          => 'Product created successfully',
            'delete-failed'           => 'Product deleted Failed',
            'delete-success'          => 'Product deleted successfully',
            'product-copied'          => 'Product copied successfully',
            'saved-inventory-message' => 'Product saved successfully',
            'update-success'          => 'Product updated successfully',
        ],

        'attributes' => [
            'index' => [
                'create-btn' => 'Create Attributes',
                'title'      => 'Attributes',

                'datagrid' => [
                    'boolean'             => 'Boolean',
                    'channel-based'       => 'Channel Based',
                    'checkbox'            => 'Checkbox',
                    'code'                => 'Code',
                    'created-at'          => 'Created At',
                    'date'                => 'Date',
                    'date-time'           => 'Date Time',
                    'delete'              => 'Delete',
                    'edit'                => 'Edit',
                    'false'               => 'False',
                    'file'                => 'File',
                    'id'                  => 'ID',
                    'image'               => 'Image',
                    'locale-based'        => 'Locale Based',
                    'mass-delete-success' => 'Selected Attribute Deleted Successfully',
                    'multiselect'         => 'Multiselect',
                    'name'                => 'Name',
                    'price'               => 'Price',
                    'required'            => 'Required',
                    'select'              => 'Select',
                    'text'                => 'Text',
                    'textarea'            => 'Textarea',
                    'true'                => 'True',
                    'type'                => 'Type',
                    'unique'              => 'Unique',
                ],
            ],

            'create' => [
                'add-attribute-options' => 'Add Attribute Options',
                'add-option'            => 'Add Option',
                'add-options-info'      => 'To create various combination of Attribute Option on a go.',
                'add-row'               => 'Add Row',
                'admin'                 => 'Admin',
                'admin-name'            => 'Admin Name',
                'back-btn'              => 'Back',
                'boolean'               => 'Boolean',
                'checkbox'              => 'Checkbox',
                'code'                  => 'Attribute Code',
                'color'                 => 'Color',
                'configuration'         => 'Configuration',
                'create-empty-option'   => 'Create default empty option',
                'date'                  => 'Date',
                'datetime'              => 'Datetime',
                'decimal'               => 'Decimal',
                'default-value'         => 'Default Value',
                'option-deleted'        => 'Option Deleted Successfully',
                'email'                 => 'Email',
                'enable-wysiwyg'        => 'Enable Wysiwyg Editor',
                'file'                  => 'File',
                'general'               => 'General',
                'image'                 => 'Image',
                'input-options'         => 'Input Options',
                'input-validation'      => 'Input Validation',
                'is-comparable'         => 'Attribute is comparable',
                'is-configurable'       => 'Use To Create Configurable Product',
                'is-filterable'         => 'Use in Layered Navigation',
                'is-required'           => 'Is Required',
                'is-unique'             => 'Is Unique',
                'is-visible-on-front'   => 'Visible on Product View Page on Front-end',
                'label'                 => 'Label',
                'multiselect'           => 'Multiselect',
                'no'                    => 'No',
                'numeric'               => 'Number',
                'options'               => 'Options',
                'position'              => 'Position',
                'price'                 => 'Price',
                'regex'                 => 'Regex',
                'regex-info'            => 'Expression should be in double quotes.',
                'save-btn'              => 'Save Attribute',
                'select'                => 'Select',
                'select-type'           => 'Select Attribute Type',
                'swatch'                => 'Swatch',
                'text'                  => 'Text',
                'textarea'              => 'Textarea',
                'title'                 => 'Add Attribute',
                'type'                  => 'Attribute Type',
                'url'                   => 'URL',
                'use-in-flat'           => 'Create in Product Flat Table',
                'validations'           => 'Validations',
                'value-per-channel'     => 'Value Per Channel',
                'value-per-locale'      => 'Value Per Locale',
                'yes'                   => 'Yes',

                'option'                => [
                    'color'    => 'Color Swatch',
                    'dropdown' => 'Dropdown',
                    'image'    => 'Image Swatch',
                    'save-btn' => 'Save Option',
                    'text'     => 'Text Swatch',
                ],
            ],

            'edit' => [
                'add-attribute-options' => 'Add Attribute Options',
                'add-option'            => 'Add Option',
                'add-options-info'      => 'To create various combination of Attribute Option on a go.',
                'add-row'               => 'Add Row',
                'admin'                 => 'Admin',
                'admin-name'            => 'Admin Name',
                'back-btn'              => 'Back',
                'boolean'               => 'Boolean',
                'checkbox'              => 'Checkbox',
                'code'                  => 'Attribute Code',
                'color'                 => 'Color',
                'configuration'         => 'Configuration',
                'create-empty-option'   => 'Create default empty option',
                'date'                  => 'Date',
                'datetime'              => 'Datetime',
                'decimal'               => 'Decimal',
                'default-value'         => 'Default Value',
                'option-deleted'        => 'Option Deleted Successfully',
                'email'                 => 'Email',
                'enable-wysiwyg'        => 'Enable Wysiwyg Editor',
                'file'                  => 'File',
                'general'               => 'General',
                'image'                 => 'Image',
                'input-options'         => 'Input Options',
                'input-validation'      => 'Input Validation',
                'is-comparable'         => 'Attribute is comparable',
                'is-configurable'       => 'Use To Create Configurable Product',
                'is-filterable'         => 'Use in Layered Navigation',
                'is-required'           => 'Is Required',
                'is-unique'             => 'Is Unique',
                'is-visible-on-front'   => 'Visible on Product View Page on Front-end',
                'label'                 => 'Label',
                'multiselect'           => 'Multiselect',
                'no'                    => 'No',
                'numeric'               => 'Number',
                'options'               => 'Options',
                'position'              => 'Position',
                'price'                 => 'Price',
                'regex'                 => 'Regex',
                'regex-info'            => 'Expression should be in double quotes.',
                'save-btn'              => 'Save Attribute',
                'select'                => 'Select',
                'select-type'           => 'Select Attribute Type',
                'swatch'                => 'Swatch',
                'text'                  => 'Text',
                'textarea'              => 'Textarea',
                'title'                 => 'Edit Attribute',
                'type'                  => 'Attribute Type',
                'url'                   => 'URL',
                'use-in-flat'           => 'Create in Product Flat Table',
                'validations'           => 'Validations',
                'value-per-channel'     => 'Value Per Channel',
                'value-per-locale'      => 'Value Per Locale',
                'yes'                   => 'Yes',

                'option' => [
                    'color'    => 'Color Swatch',
                    'dropdown' => 'Dropdown',
                    'image'    => 'Image Swatch',
                    'save-btn' => 'Save Option',
                    'text'     => 'Text Swatch',
                ],
            ],

            'create-success'    => 'Attribute Created Successfully',
            'delete-failed'     => 'Attribute Deleted Failed',
            'delete-success'    => 'Attribute Deleted Successfully',
            'update-success'    => 'Attribute Updated Successfully',
            'user-define-error' => 'Can not delete system Attribute',
        ],

        'categories' => [
            'index' => [
                'add-btn' => 'Create Category',
                'title'   => 'Categories',

                'datagrid' => [
                    'active'         => 'Active',
                    'delete-success' => 'Selected :resource were successfully deleted',
                    'delete'         => 'Delete',
                    'edit'           => 'Edit',
                    'id'             => 'ID',
                    'inactive'       => 'Inactive',
                    'name'           => 'Name',
                    'no-of-products' => 'Number of Products',
                    'position'       => 'Position',
                    'status'         => 'Visible In Menu',
                    'update-status'  => 'Update Status',
                ],
            ],

            'create' => [
                'add-banner'               => 'Add Banner',
                'add-logo'                 => 'Add Logo',
                'back-btn'                 => 'Back',
                'banner'                   => 'Banner',
                'banner-size'              => 'Banner aspect ration (1320px X 300px)',
                'description'              => 'Description',
                'description-and-images'   => 'Description and Images',
                'description-only'         => 'Description Only',
                'display-mode'             => 'Display Mode',
                'enter-position'           => 'Enter Position',
                'filterable-attributes'    => 'Filterable Attributes',
                'general'                  => 'General',
                'logo'                     => 'Logo',
                'logo-size'                => 'Logo resolution should be (110px X 110px)',
                'meta-description'         => 'Meta Description',
                'meta-keywords'            => 'Meta Keywords',
                'meta-title'               => 'Meta Title',
                'name'                     => 'Name',
                'parent-category'          => 'Parent Category',
                'position'                 => 'Position',
                'products-and-description' => 'Products and Description',
                'products-only'            => 'Products Only',
                'save-btn'                 => 'Save Category',
                'select-display-mode'      => 'Select Display Mode',
                'seo-details'              => 'SEO Details',
                'settings'                 => 'Settings',
                'slug'                     => 'Slug',
                'title'                    => 'Add New Category',
                'visible-in-menu'          => 'Visible In Menu',
            ],

            'edit' => [
                'add-banner'               => 'Add Banner',
                'add-logo'                 => 'Add Logo',
                'back-btn'                 => 'Back',
                'banner'                   => 'Banner',
                'banner-size'              => 'Banner aspect ration (1320px X 300px)',
                'description'              => 'Description',
                'description-and-images'   => 'Description and Images',
                'description-only'         => 'Description Only',
                'display-mode'             => 'Display Mode',
                'enter-position'           => 'Enter Position',
                'filterable-attributes'    => 'Filterable Attributes',
                'general'                  => 'General',
                'logo'                     => 'Logo',
                'logo-size'                => 'Logo resolution should be (110px X 110px)',
                'meta-description'         => 'Meta Description',
                'meta-keywords'            => 'Meta Keywords',
                'meta-title'               => 'Meta Title',
                'name'                     => 'Name',
                'position'                 => 'Position',
                'products-and-description' => 'Products and Description',
                'products-only'            => 'Products Only',
                'save-btn'                 => 'Save Category',
                'select-display-mode'      => 'Select Display Mode',
                'select-parent-category'   => 'Select Parent Category',
                'seo-details'              => 'SEO Details',
                'settings'                 => 'Settings',
                'slug'                     => 'Slug',
                'title'                    => 'Edit Category',
                'visible-in-menu'          => 'Visible In Menu',
            ],

            'category'             => 'Category',
            'create-success'       => 'Category created successfully.',
            'delete-category-root' => 'The Root category can not be deleted.',
            'delete-failed'        => 'Error encountered while deleting category',
            'delete-success'       => 'The category has been successfully deleted.',
            'update-success'       => 'Category updated successfully.',
        ],

        'families' => [
            'index' => [
                'add'   => 'Create Attribute Family',
                'title' => 'Families',

                'datagrid' => [
                    'code'           => 'Code',
                    'delete'         => 'Delete',
                    'delete-success' => 'Selected :resource were successfully deleted',
                    'edit'           => 'Edit',
                    'id'             => 'ID',
                    'method-error'   => 'Error! Wrong method detected, please check mass action configuration',
                    'name'           => 'Name',
                    'no-resource'    => 'The resource provided for insufficient for the action',
                    'partial-action' => 'Some actions were not performed due restricted system constraints on :resource',
                    'update-success' => 'Selected :resource were successfully updated',
                ],
            ],

            'create' => [
                'add-group-btn'                    => 'Add Group',
                'add-group-title'                  => 'Add New Group',
                'back-btn'                         => 'Back',
                'code'                             => 'Code',
                'column'                           => 'Column',
                'delete-group-btn'                 => 'Delete Group',
                'edit-group-info'                  => 'Double Click to edit Group',
                'enter-code'                       => 'Enter Code',
                'enter-name'                       => 'Enter Name',
                'general'                          => 'General',
                'group-code-already-exists'        => 'An attribute group code already exists.',
                'group-contains-system-attributes' => 'This group contains system attributes. First move system attributes to another group and try again.',
                'group-name-already-exists'        => 'An attribute group name already exists.',
                'groups'                           => 'Groups',
                'groups-info'                      => 'Manage attribute family groups',
                'main-column'                      => 'Main Column',
                'name'                             => 'Name',
                'removal-not-possible'             => 'You can not remove system attributes from attribute family.',
                'right-column'                     => 'Right Side Column',
                'save-btn'                         => 'Save Attribute Family',
                'select-group'                     => 'Please select an attribute group.',
                'title'                            => 'Create Attribute Family',
                'unassigned-attributes'            => 'Unassigned Attributes',
                'unassigned-attributes-info'       => 'Drag these attribute to add into columns or groups.',
            ],

            'edit' => [
                'add-group-btn'                    => 'Add Group',
                'add-group-title'                  => 'Add New Group',
                'back-btn'                         => 'Back',
                'code'                             => 'Code',
                'column'                           => 'Column',
                'delete-group-btn'                 => 'Delete Group',
                'edit-group-info'                  => 'Double Click to edit Group',
                'enter-code'                       => 'Enter Code',
                'enter-name'                       => 'Enter Name',
                'general'                          => 'General',
                'group-code-already-exists'        => 'An attribute group code already exists.',
                'group-contains-system-attributes' => 'This group contains system attributes. First move system attributes to another group and try again.',
                'group-name-already-exists'        => 'An attribute group name already exists.',
                'groups'                           => 'Groups',
                'groups-info'                      => 'Manage attribute family groups',
                'main-column'                      => 'Main Column',
                'name'                             => 'Name',
                'removal-not-possible'             => 'You can not remove system attributes from attribute family.',
                'right-column'                     => 'Right Side Column',
                'save-btn'                         => 'Save Attribute Family',
                'select-group'                     => 'Please select an attribute group.',
                'title'                            => 'Edit Attribute Family',
                'unassigned-attributes'            => 'Unassigned Attributes',
                'unassigned-attributes-info'       => 'Drag these attribute to add into columns or groups.',
            ],

            'attribute-family'        => 'Attribute Family',
            'attribute-product-error' => 'family is used in products.',
            'create-success'          => 'Family created successfully.',
            'delete-failed'           => 'Error encountered while deleting Family.',
            'delete-success'          => 'Family deleted successfully.',
            'family'                  => 'Family',
            'last-delete-error'       => 'At least one family is required.',
            'update-success'          => 'Family updated successfully.',
            'user-define-error'       => 'Can not delete system Attribute family',
        ],
    ],

    'enquiries' => [
        'index' => [
            'title' => 'Enquiries',
            'mass-delete-success' => ':count enquiries deleted successfully.',
            'mass-update-success' => ':count enquiries updated to :status successfully.',
            'datagrid' => [
                'id'             => 'ID',
                'user'           => 'User',
                'product'        => 'Product',
                'status'         => 'Status',
                'created-at'     => 'Created At',
                'updated-at'     => 'Updated At',
                'enquired-at'    => 'Enquired At',
                'vendor'         => 'Vendor',
                'delete'         => 'Delete',
                'update-status'  => 'Update Status',
            ],
            'status' => [
                'pending'   => 'Pending',
                'in-progress'  => 'In Progress',
                'closed' => 'Closed',
                'unknown' => 'Unknown',
            ],
        ],

        'vendor' => [
            'index' => [
                'title' => 'My Product Enquiries',
            ],
            'show' => [
                'title' => 'Enquiry Details',
                'customer-info' => 'Customer Information',
                'product-info' => 'Product Information',
                'enquiry-status' => 'Enquiry Status',
                'back-to-enquiries' => 'Back to Enquiries',
            ],
        ],
    ],

    'subscriptions' => [
        'create-success' => 'Subscription Plan Created Successfully',
        'destroy-success' => 'Subscription Plan Deleted Successfully',
        'index' => [
            'title' => 'Subscriptions',
            'create-plan-button' => 'Create Subscription Plan',
            'your-plans' => 'Your Plans',
            'your-plans' => 'Your Plans',
            'datagrid' => [
                's-no'             => 'S.no',
                'plan'           => 'Plan',
                'plan_desc'      => 'Plan Description',
                'price'     => 'Price',
                'validity'     => 'Validity',
                'subscribe'    => 'Subscribe',
            ],
            'status' => [
                'pending'   => 'Pending',
                'in-progress'  => 'In Progress',
                'closed' => 'Closed',
                'unknown' => 'Unknown',
            ],
        ],

        'create' => [
            'title'            => 'Create Subscription Plan',
            'description'      => 'Description',
            'plan'             => 'Plan name',
            'price'            => 'Price',
            'validity-days'    => 'Validity ( in days )',
            'validity'         => 'Validity',
            'save-btn'         => 'Save Plan'
        ],
    ],

    'transactions' => [
        'index' => [
            'title' => 'Transactions',
            'datagrid' => [
                's-no'             => 'S.no',
                'user'           => 'User',
                'plan'           => 'Plan',
                'txnid'          => 'Transaction ID',
                'orderid'        => 'Order Id',
                'paid_amount'    => 'Amount',
                'status'         => 'Status',
                'created-at'     => 'Created At',
                'updated-at'     => 'Updated At',
                'expired-at'     => 'Expired At',
            ],
            'status' => [
                'pending'   => 'Pending',
                'success'  => 'Success',
                'failed' => 'failed',
                'unknown' => 'Unknown',
            ],
        ],
    ],

    'customers' => [
        'customers' => [
            'index' => [
                'title'         => 'Customers',
                'login-message' => 'You logged in as :customer_name',

                'datagrid' => [
                    'active'         => 'Active',
                    'address'        => ':address  Address(s)',
                    'address-count'  => 'Address Count',
                    'channel'        => 'Channel',
                    'delete'         => 'Delete',
                    'delete-success' => 'Selected data successfully deleted',
                    'email'          => 'Email',
                    'gender'         => 'Gender',
                    'group'          => 'Group',
                    'id'             => 'Customer ID',
                    'id-value'       => 'ID - :id',
                    'inactive'       => 'Inactive',
                    'method-error'   => 'Error! Wrong method detected, please check mass action configuration',
                    'name'           => 'Customer Name',
                    'no-resource'    => 'The resource provided for insufficient for the action',
                    'order'          => ':order Order(s)',
                    'order-count'    => 'Order Count',
                    'order-pending'  => 'Customer have pending order',
                    'partial-action' => 'Some actions were not performed due restricted system constraints on :resource',
                    'phone'          => 'Contact Number',
                    'revenue'        => 'Revenue',
                    'status'         => 'Status',
                    'suspended'      => 'Suspended',
                    'update-status'  => 'Update Status',
                    'update-success' => 'Selected Customers successfully updated',
                ],

                'create' => [
                    'contact-number'        => 'Contact Number',
                    'create-btn'            => 'Create Customer',
                    'create-success'        => 'Customer created successfully',
                    'customer-group'        => 'Customer Group',
                    'date-of-birth'         => 'Date of Birth',
                    'email'                 => 'Email',
                    'female'                => 'Female',
                    'first-name'            => 'First Name',
                    'gender'                => 'Gender',
                    'last-name'             => 'Last Name',
                    'male'                  => 'Male',
                    'other'                 => 'Other',
                    'save-btn'              => 'Save customer',
                    'select-customer-group' => 'Select Customer Group',
                    'select-gender'         => 'Select Gender',
                    'title'                 => 'Create New Customer',
                ],
            ],

            'view' => [
                'account-delete-confirmation' => 'Are you sure you want to delete this account?',
                'active'                      => 'Active',
                'address-delete-confirmation' => 'Are you sure you want to delete this address?',
                'back-btn'                    => 'Back',
                'create-order'                => 'Create Order',
                'customer'                    => 'Customer',
                'date-of-birth'               => 'DOB - :dob',
                'default-address'             => 'Default Address',
                'delete-account'              => 'Delete Account',
                'delete'                      => 'Delete',
                'email'                       => 'Email - :email',
                'empty-description'           => 'Create New Addresses for Customer',
                'empty-title'                 => 'Add Customer Address',
                'gender'                      => 'Gender - :gender',
                'group'                       => 'Group - :group_code',
                'inactive'                    => 'Inactive',
                'login-as-customer'           => 'Login as customer',
                'note-created-success'        => 'Note Created Successfully',
                'order-create-confirmation'   => 'Are you sure you want to create order for this customer?',
                'phone'                       => 'Phone - :phone',
                'set-as-default'              => 'Set as Default',
                'suspended'                   => 'Suspended',
                'title'                       => 'Customer View',

                'address' => [
                    'count' => 'Addresses (:count)',

                    'create' => [
                        'city'               => 'City',
                        'company-name'       => 'Company Name',
                        'country'            => 'Country',
                        'create-btn'         => 'Create',
                        'create-address-btn' => 'Add New Address',
                        'default-address'    => 'Default Address',
                        'email'              => 'Email',
                        'first-name'         => 'First Name',
                        'last-name'          => 'Last Name',
                        'phone'              => 'Phone',
                        'post-code'          => 'Post Code',
                        'save-btn-title'     => 'Save Address',
                        'select-country'     => 'Select Country',
                        'state'              => 'State',
                        'street-address'     => 'Street Address',
                        'title'              => 'Create Address',
                        'vat-id'             => 'Vat ID',
                    ],

                    'edit' => [
                        'city'            => 'City',
                        'company-name'    => 'Company Name',
                        'country'         => 'Country',
                        'default-address' => 'Default Address',
                        'edit-btn'        => 'Edit',
                        'email'           => 'Email',
                        'first-name'      => 'First Name',
                        'last-name'       => 'Last Name',
                        'phone'           => 'Phone',
                        'post-code'       => 'Post Code',
                        'save-btn-title'  => 'Save Address',
                        'select-country'  => 'Select Country',
                        'state'           => 'State',
                        'street-address'  => 'Street Address',
                        'title'           => 'Edit Address',
                        'vat-id'          => 'Vat ID',
                    ],

                    'address-delete-success' => 'Address Deleted Successfully',
                    'create-success'         => 'Address Created Successfully',
                    'set-default-success'    => 'Default Address Updated Successfully',
                    'success-mass-delete'    => 'Address Mass Delete Successfully',
                    'update-success'         => 'Address Updated Successfully',
                ],

                'datagrid' => [
                    'invoices' => [
                        'empty-invoice'  => 'No Invoices Available',
                        'increment-id'   => 'Invoices ID',
                        'invoice-amount' => 'Invoices Amount',
                        'invoice-date'   => 'Invoices Date',
                        'order-id'       => 'Order ID',
                        'view'           => 'View',
                    ],

                    'orders' => [
                        'canceled'        => 'Canceled',
                        'channel-name'    => 'Channel Name',
                        'closed'          => 'Closed',
                        'completed'       => 'Completed',
                        'customer-name'   => 'Customer Name',
                        'date'            => 'Date',
                        'empty-order'     => 'No Orders Available',
                        'email'           => 'Email',
                        'fraud'           => 'Fraud',
                        'grand-total'     => 'Grand Total',
                        'location'        => 'Location',
                        'order-id'        => 'Order ID',
                        'pay-via'         => 'Pay By',
                        'pending'         => 'Pending',
                        'pending-payment' => 'Pending Payment',
                        'processing'      => 'Processing',
                        'status'          => 'Status',
                        'view'            => 'View',
                    ],

                    'reviews' => [
                        'approved'      => 'Approved',
                        'comment'       => 'Comment',
                        'created-at'    => 'Created At',
                        'disapproved'   => 'Disapproved',
                        'empty-reviews' => 'No Reviews Available',
                        'id'            => 'ID',
                        'invoice-date'  => 'Invoice Date',
                        'pending'       => 'Pending',
                        'product-id'    => 'Product ID',
                        'product-name'  => 'Product Name',
                        'rating'        => 'Rating',
                        'status'        => 'Status',
                        'title'         => 'Title',
                    ],
                ],

                'edit' => [
                    'contact-number'        => 'Contact Number',
                    'customer-group'        => 'Customer Group',
                    'date-of-birth'         => 'Date of Birth',
                    'edit-btn'              => 'Edit',
                    'email'                 => 'Email',
                    'female'                => 'Female',
                    'first-name'            => 'First Name',
                    'gender'                => 'Gender',
                    'last-name'             => 'Last Name',
                    'male'                  => 'Male',
                    'other'                 => 'Other',
                    'save-btn'              => 'Save customer',
                    'select-customer-group' => 'Select Customer Group',
                    'select-gender'         => 'Select Gender',
                    'status'                => 'Status',
                    'suspended'             => 'Suspended',
                    'title'                 => 'Edit Customer',
                ],

                'invoices' => [
                    'count'        => 'Invoices (:count)',
                    'increment-id' => '# :increment_id',
                ],

                'notes' => [
                    'add-note'              => 'Add Note',
                    'customer-not-notified' => ':date | Customer <b>Not Notified</b>',
                    'customer-notified'     => ':date | Customer <b>Notified</b>',
                    'note'                  => 'Note',
                    'note-placeholder'      => 'Write Your Note here',
                    'notify-customer'       => 'Notify Customer',
                    'submit-btn-title'      => 'Submit Note',
                ],

                'orders' => [
                    'count'         => 'Orders (:count)',
                    'increment-id'  => '# :increment_id',
                    'total-revenue' => 'Total Revenue - :revenue',
                ],

                'reviews' => [
                    'id'    => 'ID - :id',
                    'count' => 'Reviews (:count)',
                ],

                'cart' => [
                    'delete-success' => 'Cart item removed successfully.',
                ],

                'wishlist' => [
                    'delete-success' => 'Wishlist item removed successfully.',
                ],

                'compare' => [
                    'delete-success' => 'Compare item removed successfully.',
                ],
            ],

            'delete-failed'  => 'Customer Deleted Failed',
            'delete-success' => 'Customer Deleted Successfully',
            'order-pending'  => 'Orders is Pending',
            'update-success' => 'Customer Updated Successfully',
        ],

        'groups' => [
            'index' => [
                'title' => 'Groups',

                'create' => [
                    'code'       => 'Code',
                    'create-btn' => 'Create Group',
                    'name'       => 'Name',
                    'save-btn'   => 'Save Group',
                    'success'    => 'Group created successfully',
                    'title'      => 'Create new Group',
                ],

                'edit' => [
                    'delete-failed'  => 'Group Deleted Failed',
                    'delete-success' => 'Group Deleted Successfully',
                    'group-default'  => 'Default Group Can not be Deleted',
                    'success'        => 'Group Updated Successfully',
                    'title'          => 'Edit Group',
                ],

                'datagrid' => [
                    'code'   => 'Code',
                    'delete' => 'Delete',
                    'edit'   => 'Edit',
                    'id'     => 'ID',
                    'name'   => 'Name',
                ],
            ],
        ],

        'gdpr' => [
            'index' => [
                'title' => 'GDPR Request',

                'datagrid' => [
                    'completed'     => 'Completed',
                    'created-at'    => 'Created At',
                    'customer-name' => 'Customer Name',
                    'declined'      => 'Declined',
                    'delete'        => 'Delete',
                    'edit'          => 'Edit',
                    'id'            => 'ID',
                    'message'       => 'Message',
                    'pending'       => 'Pending',
                    'processing'    => 'Processing',
                    'revoked'       => 'Revoked',
                    'status'        => 'Status',
                    'type'          => 'Type',
                ],

                'modal' => [
                    'completed'  => 'Completed',
                    'declined'   => 'Declined',
                    'pending'    => 'Pending',
                    'processing' => 'Processing',
                    'status'     => 'Status',
                    'title'      => 'Edit GDPR Data Request',
                    'type'       => 'Type',
                    'message'    => 'Message',
                    'save-btn'   => 'Save',
                    'revoked'    => 'Revoked',
                ],

                'update-success'              => 'Data Request updated successfully and Email Sent to Customer.',
                'delete-success'              => 'Data Request deleted successfully.',
                'attribute-reason-error'      => 'Unable to Delete.',
                'update-success-unsent-email' => 'Data Request updated successfully But Email unsent to Customer.',
            ],
        ],

        'reviews' => [
            'index' => [
                'date'        => 'Date',
                'description' => 'Description',
                'id'          => 'Id',
                'name'        => 'Name',
                'product'     => 'Product',
                'rating'      => 'Rating',
                'status'      => 'Status',
                'title'       => 'Reviews',

                'edit' => [
                    'approved'       => 'Approved',
                    'customer'       => 'Customer',
                    'date'           => 'Date',
                    'disapproved'    => 'Disapproved',
                    'id'             => 'ID',
                    'images'         => 'Images',
                    'pending'        => 'Pending',
                    'product'        => 'Product',
                    'rating'         => 'Rating',
                    'review-comment' => 'Comment',
                    'review-title'   => 'Title',
                    'save-btn'       => 'Save',
                    'status'         => 'Status',
                    'title'          => 'Edit Review',
                    'update-success' => 'Review Update Successfully',
                ],

                'datagrid' => [
                    'approved'            => 'Approved',
                    'comment'             => 'Comment',
                    'customer-names'      => 'Name',
                    'date'                => 'Date',
                    'delete'              => 'Delete',
                    'delete-success'      => 'Review Deleted Successfully',
                    'disapproved'         => 'Disapproved',
                    'edit'                => 'Edit',
                    'id'                  => 'Id',
                    'mass-delete-error'   => 'Something went wrong',
                    'mass-delete-success' => 'Selected Review Deleted Successfully',
                    'mass-update-success' => 'Selected Review Updated Successfully',
                    'pending'             => 'Pending',
                    'product'             => 'Product',
                    'rating'              => 'Rating',
                    'review-id'           => 'ID - :review_id',
                    'status'              => 'Status',
                    'title'               => 'Title',
                    'update-status'       => 'Update Status',
                ],
            ],
        ],
    ],

    'marketing' => [
        'communications' => [
            'templates' => [
                'index' => [
                    'create-btn' => 'Create Template',
                    'title'      => 'Email Templates',

                    'datagrid' => [
                        'active'   => 'Active',
                        'draft'    => 'Draft',
                        'id'       => 'ID',
                        'inactive' => 'Inactive',
                        'name'     => 'Name',
                        'status'   => 'Status',
                    ],
                ],

                'create' => [
                    'active'         => 'Active',
                    'back-btn'       => 'Back',
                    'content'        => 'Content',
                    'create-success' => 'Email template created successfully.',
                    'draft'          => 'Draft',
                    'general'        => 'General',
                    'inactive'       => 'Inactive',
                    'name'           => 'Name',
                    'save-btn'       => 'Save Template',
                    'select-status'  => 'Select Status',
                    'status'         => 'Status',
                    'title'          => 'Create Template',
                ],

                'edit' => [
                    'active'         => 'Active',
                    'back-btn'       => 'Back',
                    'content'        => 'Content',
                    'draft'          => 'Draft',
                    'general'        => 'General',
                    'inactive'       => 'Inactive',
                    'name'           => 'Name',
                    'save-btn'       => 'Save Template',
                    'status'         => 'Status',
                    'title'          => 'Edit Template',
                    'update-success' => 'Updated successfully',
                ],

                'delete-failed'  => ':name Deleted Failed',
                'delete-success' => 'Template Deleted successfully',
                'email-template' => 'Email Template',
            ],

            'campaigns' => [
                'index' => [
                    'create-btn' => 'Create Campaign',
                    'title'      => 'Campaigns',

                    'datagrid' => [
                        'active'   => 'Active',
                        'delete'   => 'Delete',
                        'edit'     => 'Edit',
                        'id'       => 'ID',
                        'inactive' => 'Inactive',
                        'name'     => 'Name',
                        'status'   => 'Status',
                        'subject'  => 'Subject',
                    ],
                ],

                'create' => [
                    'active'          => 'Active',
                    'back-btn'        => 'Back',
                    'channel'         => 'Channel',
                    'customer-group'  => 'Customer Group',
                    'email-template'  => 'Email Template',
                    'event'           => 'Event',
                    'general'         => 'General',
                    'inactive'        => 'Inactive',
                    'name'            => 'Name',
                    'save-btn'        => 'Save Campaign',
                    'select-channel'  => 'Select Channel',
                    'select-event'    => 'Select Event',
                    'select-group'    => 'Select Group',
                    'select-status'   => 'Select Status',
                    'select-template' => 'Select Template',
                    'setting'         => 'Setting',
                    'status'          => 'Status',
                    'subject'         => 'Subject',
                    'title'           => 'Create Campaign',
                ],

                'edit' => [
                    'active'          => 'Active',
                    'audience'        => 'Audience',
                    'back-btn'        => 'Back',
                    'channel'         => 'Channel',
                    'customer-group'  => 'Customer Group',
                    'email-template'  => 'Email Template',
                    'event'           => 'Event',
                    'general'         => 'General',
                    'inactive'        => 'Inactive',
                    'name'            => 'Name',
                    'save-btn'        => 'Save Campaign',
                    'select-event'    => 'Select Event',
                    'select-status'   => 'Select Status',
                    'select-template' => 'Select Template',
                    'status'          => 'Status',
                    'subject'         => 'Subject',
                    'title'           => 'Edit Campaign',
                ],

                'create-success' => 'Campaign created successfully.',
                'delete-failed'  => ':name Delete failed',
                'delete-success' => 'Campaign deleted successfully',
                'email-campaign' => 'Email Campaign',
                'update-success' => 'Campaign updated successfully.',
            ],

            'events' => [
                'index' => [
                    'create-btn' => 'Create Event',
                    'event'      => 'Event',
                    'title'      => 'Events',

                    'datagrid' => [
                        'actions' => 'Actions',
                        'date'    => 'Date',
                        'delete'  => 'Delete',
                        'edit'    => 'Edit',
                        'id'      => 'ID',
                        'name'    => 'Name',
                    ],

                    'create' => [
                        'date'           => 'Date',
                        'delete-warning' => 'Are you sure, you want to perform this action?',
                        'description'    => 'Description',
                        'general'        => 'General',
                        'name'           => 'Name',
                        'save-btn'       => 'Save Event',
                        'success'        => 'Events Created Successfully',
                        'title'          => 'Create Events',
                    ],

                    'edit' => [
                        'success' => 'Events Updated Successfully',
                        'title'   => 'Edit Events',
                    ],
                ],

                'delete-failed'  => ':name Delete Failed',
                'delete-success' => 'Events Deleted Successfully',
                'edit-error'     => 'Event can not be Edit',
            ],

            'subscribers' => [
                'index' => [
                    'title' => 'Newsletter Subscriptions',

                    'datagrid' => [
                        'actions'    => 'Actions',
                        'delete'     => 'Delete',
                        'edit'       => 'Edit',
                        'email'      => 'Email',
                        'false'      => 'False',
                        'id'         => 'ID',
                        'subscribed' => 'Subscribed',
                        'true'       => 'True',
                    ],

                    'edit' => [
                        'back-btn'      => 'Back',
                        'email'         => 'Email',
                        'false'         => 'False',
                        'save-btn'      => 'Save Subscriber',
                        'subscribed'    => 'Subscribed',
                        'success'       => 'Newsletter Subscription Updated Successfully',
                        'title'         => 'Edit Newsletter Subscriber',
                        'true'          => 'True',
                        'update-failed' => 'Newsletter Subscription Not Updated',
                    ],
                ],

                'delete-failed'  => 'Subscriber Deleted Failure',
                'delete-success' => 'Subscriber Deleted Successfully',
                'delete-warning' => 'Are you sure, you want to perform this action?',
            ],
        ],

        'promotions' => [
            'index' => [
                'cart-rule-title'    => 'Cart Rules',
                'catalog-rule-title' => 'Catalog Rules',
            ],

            'cart-rules' => [
                'index' => [
                    'create-btn' => 'Create Cart Rule',
                    'title'      => 'Cart Rules',

                    'datagrid' => [
                        'active'      => 'Active',
                        'copy'        => 'Copy',
                        'copy-of'     => ':value',
                        'coupon-code' => 'Coupon Code',
                        'delete'      => 'Delete',
                        'draft'       => 'Draft',
                        'edit'        => 'Edit',
                        'end'         => 'End',
                        'id'          => 'ID',
                        'inactive'    => 'Inactive',
                        'name'        => 'Name',
                        'priority'    => 'Priority',
                        'start'       => 'Start',
                        'status'      => 'Status',
                    ],
                ],

                'create' => [
                    'action-type'                               => 'Action Type',
                    'actions'                                   => 'Actions',
                    'add-condition'                             => 'Add Condition',
                    'additional'                                => 'Additional',
                    'all-conditions-true'                       => 'All Conditions Are True',
                    'any-conditions-true'                       => 'Any Conditions Are True',
                    'apply-to-shipping'                         => 'Apply to Shipping',
                    'attribute-family'                          => 'Attribute Family',
                    'attribute-name-children-only'              => 'Attribute name Children Only',
                    'attribute-name-parent-only'                => 'Attribute name Parent Only',
                    'auto-generate-coupon'                      => 'Auto Generate Coupon',
                    'back-btn'                                  => 'Back',
                    'buy-x-get-y-free'                          => 'Buy X Get Y Free',
                    'buy-x-quantity'                            => 'Buy X Quantity',
                    'cart-attribute'                            => 'Cart attribute',
                    'cart-item-attribute'                       => 'Cart item attribute',
                    'categories'                                => 'Categories',
                    'channels'                                  => 'Channels',
                    'children-categories'                       => 'Categories(Children Only)',
                    'choose-condition-to-add'                   => 'Choose condition to add',
                    'condition-type'                            => 'Condition Type',
                    'conditions'                                => 'Conditions',
                    'contain'                                   => 'Contain',
                    'contains'                                  => 'Contains',
                    'coupon-code'                               => 'Coupon Code',
                    'coupon-type'                               => 'Coupon Type',
                    'create-success'                            => 'Cart rule created successfully',
                    'customer-groups'                           => 'Customer Groups',
                    'description'                               => 'Description',
                    'discount-amount'                           => 'Discount Amount',
                    'does-not-contain'                          => 'Does not contain',
                    'end-of-other-rules'                        => 'End Of Other Rules',
                    'equals-or-greater-than'                    => 'Equals or greater than',
                    'equals-or-less-than'                       => 'Equals or less than',
                    'fixed-amount'                              => 'Fixed Amount',
                    'fixed-amount-whole-cart'                   => 'Fixed Amount Whole Cart',
                    'free-shipping'                             => 'Free Shipping',
                    'from'                                      => 'From',
                    'general'                                   => 'General',
                    'greater-than'                              => 'Greater than',
                    'is-equal-to'                               => 'Is equal to',
                    'is-not-equal-to'                           => 'Is not equal to',
                    'less-than'                                 => 'Less than',
                    'marketing-time'                            => 'Marketing Time',
                    'maximum-quantity-allowed-to-be-discounted' => 'Maximum Quantity allowed to be discounted',
                    'name'                                      => 'Name',
                    'no'                                        => 'No',
                    'no-coupon'                                 => 'No Coupon',
                    'parent-categories'                         => 'Categories(Parent Only)',
                    'payment-method'                            => 'Payment method',
                    'percentage-product-price'                  => 'Percentage Product Price',
                    'price-in-cart'                             => 'Price in cart',
                    'priority'                                  => 'Priority',
                    'product-attribute'                         => 'Product attribute',
                    'qty-in-cart'                               => 'Quantity in cart',
                    'save-btn'                                  => 'Save Cart Rule',
                    'settings'                                  => 'Settings',
                    'shipping-country'                          => 'Shipping country',
                    'shipping-method'                           => 'Shipping method',
                    'shipping-postcode'                         => 'Shipping postcode',
                    'shipping-state'                            => 'Shipping state',
                    'specific-coupon'                           => 'Specific Coupon',
                    'status'                                    => 'Status',
                    'subtotal'                                  => 'Subtotal',
                    'title'                                     => 'Create Cart Rule',
                    'to'                                        => 'To',
                    'total-items-qty'                           => 'Total items-qty',
                    'total-weight'                              => 'Total Weight',
                    'uses-per-coupon'                           => 'Uses Per Coupon',
                    'uses-per-customer'                         => 'Uses Per Customer',
                    'uses-per-customer-control-info'            => 'Will be used for logged in customers only.',
                    'yes'                                       => 'Yes',
                ],

                'edit' => [
                    'action-type'                               => 'Action Type',
                    'actions'                                   => 'Actions',
                    'add-condition'                             => 'Add Condition',
                    'additional'                                => 'Additional',
                    'all-conditions-true'                       => 'All Conditions Are True',
                    'alphabetical'                              => 'Alphabetical',
                    'alphanumeric'                              => 'Alphanumeric',
                    'any-conditions-true'                       => 'Any Conditions Are True',
                    'apply-to-shipping'                         => 'Apply to Shipping',
                    'attribute-family'                          => 'Attribute Family',
                    'attribute-name-children-only'              => 'Attribute name children only',
                    'attribute-name-parent-only'                => 'Attribute name parent only',
                    'auto-generate-coupon'                      => 'Auto Generate Coupon',
                    'back-btn'                                  => 'Back',
                    'buy-x-get-y-free'                          => 'Buy X Get Y Free',
                    'buy-x-quantity'                            => 'Buy X Quantity',
                    'cart-attribute'                            => 'Cart attribute',
                    'cart-item-attribute'                       => 'Cart item attribute',
                    'categories'                                => 'Categories',
                    'channels'                                  => 'Channels',
                    'children-categories'                       => 'Children Categories',
                    'choose-condition-to-add'                   => 'Choose condition to add',
                    'code-format'                               => 'Code Format',
                    'code-prefix'                               => 'Code Prefix',
                    'code-suffix'                               => 'Code Suffix',
                    'condition-type'                            => 'Condition Type',
                    'conditions'                                => 'Conditions',
                    'contain'                                   => 'Contain',
                    'contains'                                  => 'Contains',
                    'coupon-code'                               => 'Coupon Code',
                    'coupon-length'                             => 'Coupon Length',
                    'coupon-qty'                                => 'Coupon Quantity',
                    'coupon-type'                               => 'Coupon Type',
                    'customer-group'                            => 'Customer Group',
                    'customer-groups'                           => 'Customer Groups',
                    'description'                               => 'Description',
                    'discount-amount'                           => 'Discount Amount',
                    'does-not-contain'                          => 'Does not contain',
                    'end-of-other-rules'                        => 'End Of Other Rules',
                    'equals-or-greater-than'                    => 'Equals or greater than',
                    'equals-or-less-than'                       => 'Equals or less than',
                    'fixed-amount'                              => 'Fixed Amount',
                    'fixed-amount-whole-cart'                   => 'Fixed Amount Whole Cart',
                    'free-shipping'                             => 'Free Shipping',
                    'from'                                      => 'From',
                    'general'                                   => 'General',
                    'generate'                                  => 'Generate',
                    'greater-than'                              => 'Greater than',
                    'is-equal-to'                               => 'Is equal to',
                    'is-not-equal-to'                           => 'Is not equal to',
                    'less-than'                                 => 'Less than',
                    'marketing-time'                            => 'Marketing Time',
                    'maximum-quantity-allowed-to-be-discounted' => 'Maximum Quantity allowed to be discounted',
                    'name'                                      => 'Name',
                    'no'                                        => 'No',
                    'no-coupon'                                 => 'No Coupon',
                    'numeric'                                   => 'Numeric',
                    'parent-categories'                         => 'Parent Categories',
                    'payment-method'                            => 'Payment method',
                    'percentage-product-price'                  => 'Percentage Product Price',
                    'price-in-cart'                             => 'Price in cart',
                    'priority'                                  => 'Priority',
                    'product-attribute'                         => 'Product attribute',
                    'qty-in-cart'                               => 'Quantity in cart',
                    'save-btn'                                  => 'Save Cart Rule',
                    'settings'                                  => 'Settings',
                    'shipping-country'                          => 'Shipping country',
                    'shipping-method'                           => 'Shipping method',
                    'shipping-postcode'                         => 'Shipping postcode',
                    'shipping-state'                            => 'Shipping state',
                    'specific-coupon'                           => 'Specific Coupon',
                    'status'                                    => 'Status',
                    'subtotal'                                  => 'Subtotal',
                    'title'                                     => 'Edit Cart Rule',
                    'to'                                        => 'To',
                    'total-items-qty'                           => 'Total items-qty',
                    'total-weight'                              => 'Total Weight',
                    'update-success'                            => 'Cart rule updated successfully',
                    'uses-per-coupon'                           => 'Uses Per Coupon',
                    'uses-per-customer'                         => 'Uses Per Customer',
                    'uses-per-customer-control-info'            => 'Will be used for logged in customers only.',
                    'yes'                                       => 'Yes',
                ],

                'delete-failed'  => 'Cart Rule Deleted Failed',
                'delete-success' => 'Cart Rule Deleted Successfully',
            ],

            'catalog-rules' => [
                'index' => [
                    'create-btn' => 'Create Catalog Rule',
                    'title'      => 'Catalog Rules',

                    'datagrid' => [
                        'active'   => 'Active',
                        'delete'   => 'Delete',
                        'edit'     => 'Edit',
                        'end'      => 'End',
                        'id'       => 'ID',
                        'inactive' => 'Inactive',
                        'name'     => 'Name',
                        'priority' => 'Priority',
                        'start'    => 'Start',
                        'status'   => 'Status',
                    ],
                ],

                'create' => [
                    'action-type'              => 'Action Type',
                    'actions'                  => 'Actions',
                    'add-condition'            => 'Add Condition',
                    'all-conditions-true'      => 'All Conditions are true',
                    'any-conditions-true'      => 'Any Conditions are true',
                    'attribute-family'         => 'Attribute Family',
                    'back-btn'                 => 'Back',
                    'categories'               => 'Categories',
                    'channels'                 => 'Channels',
                    'choose-condition-to-add'  => 'Choose Condition To Add',
                    'condition-type'           => 'Condition Type',
                    'conditions'               => 'Conditions',
                    'contain'                  => 'Contain',
                    'contains'                 => 'Contains',
                    'customer-groups'          => 'Customer Groups',
                    'description'              => 'Description',
                    'discount-amount'          => 'Discount Amount',
                    'does-not-contain'         => 'Does not contain',
                    'end-other-rules'          => 'End other rules',
                    'equals-or-greater-than'   => 'Equals or greater than',
                    'equals-or-less-than'      => 'Equals or less than',
                    'fixed-amount'             => 'Fixed Amount',
                    'from'                     => 'From',
                    'general'                  => 'General',
                    'greater-than'             => 'Greater than',
                    'is-equal-to'              => 'Is equal to',
                    'is-not-equal-to'          => 'Is not equal to',
                    'less-than'                => 'Less than',
                    'marketing-time'           => 'Marketing Time',
                    'name'                     => 'Name',
                    'no'                       => 'No',
                    'percentage-product-price' => 'Percentage of Product Price',
                    'priority'                 => 'Priority',
                    'product-attribute'        => 'Product attribute',
                    'save-btn'                 => 'Save Catalog Rule',
                    'settings'                 => 'Settings',
                    'status'                   => 'Status',
                    'title'                    => 'Create Catalog Rule',
                    'to'                       => 'To',
                    'yes'                      => 'Yes',
                ],

                'edit' => [
                    'action-type'              => 'Action Type',
                    'actions'                  => 'Actions',
                    'add-condition'            => 'Add Condition',
                    'all-conditions-true'      => 'All Conditions are true',
                    'any-conditions-true'      => 'Any Conditions are true',
                    'back-btn'                 => 'Back',
                    'categories'               => 'Categories',
                    'channels'                 => 'Channels',
                    'choose-condition-to-add'  => 'Choose Condition To Add',
                    'condition-type'           => 'Condition Type',
                    'conditions'               => 'Conditions',
                    'contain'                  => 'Contain',
                    'contains'                 => 'Contains',
                    'customer-groups'          => 'Customer Groups',
                    'description'              => 'Description',
                    'discount-amount'          => 'Discount Amount',
                    'does-not-contain'         => 'Does not contain',
                    'end-other-rules'          => 'End other rules',
                    'equals-or-greater-than'   => 'Equals or greater than',
                    'equals-or-less-than'      => 'Equals or less than',
                    'fixed-amount'             => 'Fixed Amount',
                    'from'                     => 'From',
                    'general'                  => 'General',
                    'greater-than'             => 'Greater than',
                    'is-equal-to'              => 'Is equal to',
                    'is-not-equal-to'          => 'Is not equal to',
                    'less-than'                => 'Less than',
                    'marketing-time'           => 'Marketing Time',
                    'name'                     => 'Name',
                    'no'                       => 'No',
                    'percentage-product-price' => 'Percentage of Product Price',
                    'priority'                 => 'Priority',
                    'product-attribute'        => 'Product attribute',
                    'save-btn'                 => 'Save Catalog Rule',
                    'settings'                 => 'Settings',
                    'status'                   => 'Status',
                    'title'                    => 'Edit Catalog Rule',
                    'to'                       => 'To',
                    'yes'                      => 'Yes',
                ],

                'create-success' => 'Catalog rule created successfully',
                'delete-success' => 'Catalog rule deleted successfully',
                'update-success' => 'Catalog rule updated successfully',
            ],

            'cart-rules-coupons' => [
                'cart-rule-not-defined-error' => 'Cart rule can not be deleted',
                'delete-success'              => 'Cart Rule Coupon Deleted Successfully',
                'mass-delete-success'         => 'Selected items Delete Successfully',
                'success'                     => ':name Successfully Created',

                'datagrid' => [
                    'coupon-code'     => 'Coupon Code',
                    'created-date'    => 'Created Date',
                    'delete'          => 'Delete',
                    'expiration-date' => 'Expiration Date',
                    'id'              => 'ID',
                    'times-used'      => 'Times Used',
                ],
            ],
        ],

        'search-seo' => [
            'search-terms' => [
                'index' => [
                    'create-btn' => 'Create Search Term',
                    'title'      => 'Search Terms',

                    'datagrid' => [
                        'uses'                => 'Uses',
                        'search-query'        => 'Search Query',
                        'results'             => 'Results',
                        'redirect-url'        => 'Redirect Url',
                        'mass-delete-success' => 'Selected Search Terms Deleted Successfully',
                        'locale'              => 'Locale',
                        'id'                  => 'ID',
                        'edit'                => 'Edit',
                        'delete'              => 'Delete',
                        'channel'             => 'Channel',
                        'actions'             => 'Actions',
                    ],

                    'create' => [
                        'channel'        => 'Channel',
                        'delete-warning' => 'Are you sure, you want to perform this action?',
                        'locale'         => 'Locale',
                        'redirect-url'   => 'Redirect Url',
                        'results'        => 'Results',
                        'save-btn'       => 'Save Search Term',
                        'search-query'   => 'Search Query',
                        'success'        => 'Search Term created successfully',
                        'title'          => 'Create Search Term',
                        'uses'           => 'Uses',
                    ],

                    'edit' => [
                        'delete-success' => 'Search Term deleted successfully',
                        'success'        => 'Search Term updated successfully',
                        'title'          => 'Edit Search Term',
                    ],
                ],
            ],

            'search-synonyms' => [
                'index' => [
                    'create-btn' => 'Create Search Synonym',
                    'title'      => 'Search Synonyms',

                    'datagrid' => [
                        'actions'             => 'Actions',
                        'delete'              => 'Delete',
                        'edit'                => 'Edit',
                        'id'                  => 'ID',
                        'mass-delete-success' => 'Selected Search Synonyms Deleted Successfully',
                        'name'                => 'Name',
                        'terms'               => 'Terms',
                    ],

                    'create' => [
                        'delete-warning' => 'Are you sure, you want to perform this action?',
                        'name'           => 'Name',
                        'save-btn'       => 'Save Search Synonym',
                        'success'        => 'Search Synonym created successfully',
                        'terms'          => 'Terms',
                        'terms-info'     => 'Enter synonyms as a comma-separated list, e.g., "shoes,footwear." This expands the search to include both terms.',
                        'title'          => 'Create Search Synonym',
                    ],

                    'edit' => [
                        'delete-success' => 'Search Synonym deleted successfully',
                        'success'        => 'Search Synonym updated successfully',
                        'title'          => 'Edit Search Synonym',
                    ],
                ],
            ],

            'sitemaps' => [
                'index' => [
                    'create-btn' => 'Create Sitemap',
                    'sitemap'    => 'Sitemap',
                    'title'      => 'Sitemaps',

                    'datagrid' => [
                        'actions'         => 'Actions',
                        'delete'          => 'Delete',
                        'edit'            => 'Edit',
                        'file-name'       => 'File Name',
                        'id'              => 'ID',
                        'link-for-google' => 'Link for Google',
                        'path'            => 'Path',
                    ],

                    'create' => [
                        'delete-warning' => 'Are you sure, you want to perform this action?',
                        'file-name'      => 'File Name',
                        'file-name-info' => 'Example: sitemap.xml',
                        'path'           => 'Path',
                        'path-info'      => 'Example: "/sitemap/" or "/" for base path',
                        'save-btn'       => 'Save Sitemap',
                        'success'        => 'Sitemap created successfully',
                        'title'          => 'Create Sitemap',
                    ],

                    'edit' => [
                        'delete-success' => 'Sitemap Deleted successfully',
                        'success'        => 'Sitemap Updated successfully',
                        'title'          => 'Edit Sitemap',
                    ],
                ],

                'edit' => [
                    'back-btn'       => 'Back',
                    'file-name'      => 'File Name',
                    'file-name-info' => 'Example: sitemap.xml',
                    'general'        => 'General',
                    'path'           => 'Path',
                    'path-info'      => 'Example: "/sitemap/" or "/" for base path',
                    'save-btn'       => 'Save Sitemap',
                ],

                'delete-failed' => ':name Deleted Failed',
            ],

            'url-rewrites' => [
                'index' => [
                    'create-btn' => 'Create URL Rewrite',
                    'title'      => 'URL Rewrites',

                    'datagrid' => [
                        'actions'             => 'Actions',
                        'category'            => 'Category',
                        'cms-page'            => 'CMS Page',
                        'delete'              => 'Delete',
                        'edit'                => 'Edit',
                        'for'                 => 'For',
                        'id'                  => 'ID',
                        'locale'              => 'Locale',
                        'mass-delete-success' => 'Selected URL Rewrites Deleted Successfully',
                        'permanent-redirect'  => 'Permanent (301)',
                        'product'             => 'Product',
                        'redirect-type'       => 'Redirect Type',
                        'request-path'        => 'Request Path',
                        'target-path'         => 'Target Path',
                        'temporary-redirect'  => 'Temporary (302)',
                    ],

                    'create' => [
                        'category'           => 'Category',
                        'cms-page'           => 'CMS Page',
                        'delete-warning'     => 'Are you sure, you want to perform this action?',
                        'for'                => 'For',
                        'locale'             => 'Locale',
                        'permanent-redirect' => 'Permanent (301)',
                        'product'            => 'Product',
                        'redirect-type'      => 'Redirect Type',
                        'request-path'       => 'Request Path',
                        'save-btn'           => 'Save URL Rewrite',
                        'success'            => 'URL Rewrite created successfully',
                        'target-path'        => 'Target Path',
                        'temporary-redirect' => 'Temporary (302)',
                        'title'              => 'Create URL Rewrite',
                    ],

                    'edit' => [
                        'delete-success' => 'URL Rewrite deleted successfully',
                        'success'        => 'URL Rewrite updated successfully',
                        'title'          => 'Edit URL Rewrite',
                    ],
                ],
            ],
        ],
    ],

    'cms' => [
        'index' => [
            'already-taken' => 'The :name has already been taken.',
            'create-btn'    => 'Create Page',
            'channel'       => 'Channel',
            'language'      => 'Language',
            'title'         => 'Pages',

            'datagrid' => [
                'channel'             => 'Channel',
                'delete'              => 'Delete',
                'edit'                => 'Edit',
                'id'                  => 'ID',
                'mass-delete-success' => 'Selected Data Deleted Successfully',
                'page-title'          => 'Page Title',
                'url-key'             => 'URL Key',
                'view'                => 'View',
            ],
        ],

        'create' => [
            'channels'         => 'Channels',
            'content'          => 'Content',
            'description'      => 'Description',
            'general'          => 'General',
            'meta-description' => 'Meta Description',
            'meta-keywords'    => 'Meta Keywords',
            'meta-title'       => 'Meta Title',
            'page-title'       => 'Title',
            'save-btn'         => 'Save Page',
            'seo'              => 'SEO',
            'title'            => 'Create Page',
            'url-key'          => 'URL Key',
        ],

        'edit' => [
            'back-btn'         => 'Back',
            'channels'         => 'Channels',
            'content'          => 'Content',
            'description'      => 'Description',
            'general'          => 'General',
            'meta-description' => 'Meta Description',
            'meta-keywords'    => 'Meta Keywords',
            'meta-title'       => 'Meta Title',
            'page-title'       => 'Page Title',
            'preview-btn'      => 'Preview Page',
            'save-btn'         => 'Save Page',
            'seo'              => 'SEO',
            'title'            => 'Edit Page',
            'url-key'          => 'URL Key',
        ],

        'create-success' => 'CMS created successfully.',
        'delete-success' => 'CMS deleted successfully.',
        'no-resource'    => 'Resource not exists.',
        'update-success' => 'CMS updated successfully.',
    ],

    'settings' => [
        'locales' => [
            'index' => [
                'create-btn' => 'Create Locale',
                'locale'     => 'Locale',
                'logo-size'  => 'Image resolution should be like 24px X 16px',
                'title'      => 'Locales',

                'datagrid' => [
                    'actions'   => 'Actions',
                    'code'      => 'Code',
                    'delete'    => 'Delete',
                    'direction' => 'Direction',
                    'edit'      => 'Edit',
                    'id'        => 'ID',
                    'ltr'       => 'LTR',
                    'name'      => 'Name',
                    'rtl'       => 'RTL',
                ],

                'create' => [
                    'code'             => 'Code',
                    'direction'        => 'Direction',
                    'locale-logo'      => 'Locale Logo',
                    'name'             => 'Name',
                    'save-btn'         => 'Save Locale',
                    'select-direction' => 'Select Direction',
                    'title'            => 'Create Locale',
                ],

                'edit' => [
                    'title' => 'Edit Locales',
                ],

                'create-success'    => 'Locale created successfully.',
                'delete-failed'     => 'Locale deletion failed',
                'delete-success'    => 'Locale deleted successfully.',
                'delete-warning'    => 'Are you sure, you want to perform this action?',
                'last-delete-error' => 'At least one Locale is required.',
                'update-success'    => 'Locale updated successfully.',
            ],
        ],

        'currencies' => [
            'index' => [
                'create-btn' => 'Create Currency',
                'currency'   => 'Currency',
                'title'      => 'Currencies',

                'datagrid' => [
                    'actions'        => 'Actions',
                    'code'           => 'Code',
                    'delete'         => 'Delete',
                    'edit'           => 'Edit',
                    'id'             => 'ID',
                    'method-error'   => 'Error! Wrong method detected, please check mass action configuration',
                    'name'           => 'Name',
                    'no-resource'    => 'The resource provided for insufficient for the action',
                    'partial-action' => 'Some actions were not performed due restricted system constraints on :resource',
                    'update-success' => 'Selected :resource were successfully updated',
                ],

                'create' => [
                    'code'                   => 'Code',
                    'create-btn'             => 'Create Currency',
                    'currency-position'      => 'Currency Position',
                    'decimal'                => 'Decimal',
                    'decimal-separator'      => 'Decimal Separator',
                    'decimal-separator-note' => 'The :attribute field can only accept the comma (,) and dot (.) characters',
                    'delete-warning'         => 'Are you sure you want to perform this action?',
                    'general'                => 'General',
                    'group-separator'        => 'Group Separator',
                    'group-separator-note'   => 'The :attribute field can only accept the comma (,), dot (.), apostrophe (\'), and space ( ) characters',
                    'name'                   => 'Name',
                    'save-btn'               => 'Save Currency',
                    'symbol'                 => 'Symbol',
                    'title'                  => 'Create New Currency',
                ],

                'edit' => [
                    'title' => 'Edit Currency',
                ],

                'create-success'    => 'Currency created successfully.',
                'delete-failed'     => 'Currency Deleted Failed',
                'delete-success'    => 'Currency deleted successfully.',
                'last-delete-error' => 'At least one Currency is required.',
                'update-success'    => 'Currency updated successfully.',
            ],
        ],

        'data-transfer' => [
            'imports' => [
                'create' => [
                    'action'              => 'Action',
                    'allowed-errors'      => 'Allowed Errors',
                    'back-btn'            => 'Back',
                    'create-update'       => 'Create/Update',
                    'delete'              => 'Delete',
                    'download-sample'     => 'Download Sample',
                    'field-separator'     => 'Field Separator',
                    'file'                => 'File',
                    'file-info'           => 'Use relative path to /project-root/storage/app/import, e.g. product-images, import-images.',
                    'file-info-example'   => 'For example, in case product-images, files should be placed into /project-root/storage/app/import/product-images folder.',
                    'general'             => 'General',
                    'images-directory'    => 'Images Directory Path',
                    'process-in-queue'    => 'Process In Queue',
                    'results'             => 'Results',
                    'save-btn'            => 'Save Import',
                    'settings'            => 'Settings',
                    'skip-errors'         => 'Skip Errors',
                    'stop-on-errors'      => 'Stop on Errors',
                    'title'               => 'Create Import',
                    'type'                => 'Type',
                    'validation-strategy' => 'Validation Strategy',
                ],

                'edit' => [
                    'action'              => 'Action',
                    'allowed-errors'      => 'Allowed Errors',
                    'back-btn'            => 'Back',
                    'create-update'       => 'Create/Update',
                    'current-file'        => 'Current Uploaded File',
                    'delete'              => 'Delete',
                    'download-sample'     => 'Download Sample',
                    'field-separator'     => 'Field Separator',
                    'file'                => 'File',
                    'file-info'           => 'Use relative path to /project-root/storage/app/import, e.g. product-images, import-images.',
                    'file-info-example'   => 'For example, in case product-images, files should be placed into /project-root/storage/app/import/product-images folder.',
                    'general'             => 'General',
                    'images-directory'    => 'Images Directory Path',
                    'process-in-queue'    => 'Process In Queue',
                    'results'             => 'Results',
                    'save-btn'            => 'Save Import',
                    'settings'            => 'Settings',
                    'skip-errors'         => 'Skip Errors',
                    'stop-on-errors'      => 'Stop on Errors',
                    'title'               => 'Edit Import',
                    'type'                => 'Type',
                    'validation-strategy' => 'Validation Strategy',
                ],

                'index' => [
                    'button-title' => 'Create Import',
                    'title'        => 'Imports',

                    'datagrid' => [
                        'actions'       => 'Actions',
                        'completed-at'  => 'Completed At',
                        'created'       => 'Created',
                        'delete'        => 'Delete',
                        'deleted'       => 'Deleted',
                        'edit'          => 'Edit',
                        'error-file'    => 'Error File',
                        'id'            => 'ID',
                        'started-at'    => 'Started At',
                        'state'         => 'State',
                        'summary'       => 'Summary',
                        'updated'       => 'Updated',
                        'uploaded-file' => 'Uploaded File',
                    ],
                ],

                'import' => [
                    'back-btn'                => 'Back',
                    'completed-batches'       => 'Total Batches Completed:',
                    'download-error-report'   => 'Download Full Report',
                    'edit-btn'                => 'Edit',
                    'imported-info'           => 'Congratulations! Your import was successful.',
                    'importing-info'          => 'Import In Process',
                    'indexing-info'           => 'Resources Indexing (Price, Inventory and Elastic Search) In Progress',
                    'linking-info'            => 'Resources Linking In Progress',
                    'progress'                => 'Progress:',
                    'title'                   => 'Import',
                    'total-batches'           => 'Total Batches:',
                    'total-created'           => 'Total Records Created:',
                    'total-deleted'           => 'Total Records Deleted:',
                    'total-errors'            => 'Total Errors:',
                    'total-invalid-rows'      => 'Total Invalid Rows:',
                    'total-rows-processed'    => 'Total Rows Processed:',
                    'total-updated'           => 'Total Records Updated:',
                    'validate'                => 'Validate',
                    'validate-info'           => 'Click on Validate Data to check your import.',
                    'validating-info'         => 'The data started reading and Validating',
                    'validation-failed-info'  => 'Your import is invalid. Please fix the following errors and try again.',
                    'validation-success-info' => 'Your import is valid. Click on Import to start the import process.',
                ],

                'create-success'    => 'Import created successfully.',
                'delete-failed'     => 'Import deletion failed unexpectedly.',
                'delete-success'    => 'Import deleted successfully.',
                'not-valid'         => 'Import is invalid',
                'nothing-to-import' => 'There are no resources to import.',
                'setup-queue-error' => 'Please change your queue driver to "database" or "redis" to start the import process.',
                'update-success'    => 'Import updated successfully.',
            ],
        ],

        'exchange-rates' => [
            'index' => [
                'create-btn'    => 'Create Exchange Rate',
                'exchange-rate' => 'Exchange Rate',
                'title'         => 'Exchange Rates',
                'update-rates'  => 'Update Exchange Rate',

                'create' => [
                    'delete-warning'         => 'Are you sure, you want to perform this action?',
                    'rate'                   => 'Rate',
                    'save-btn'               => 'Save Exchange Rate',
                    'select-target-currency' => 'Select Target Currency',
                    'source-currency'        => 'Source Currency',
                    'target-currency'        => 'Target Currency',
                    'title'                  => 'Create Exchange Rate',
                ],

                'edit' => [
                    'title' => 'Edit Exchange Rates',
                ],

                'datagrid' => [
                    'actions'       => 'Actions',
                    'currency-name' => 'Currency Name',
                    'delete'        => 'Delete',
                    'edit'          => 'Edit',
                    'exchange-rate' => 'Exchange Rate',
                    'id'            => 'ID',
                ],

                'create-success' => 'Exchange Rate Created Successfully',
                'delete-error'   => 'Exchange Rate Deleted Error',
                'delete-success' => 'Exchange Rate Deleted Successfully',
                'update-success' => 'Exchange Rate Updated Successfully',
            ],
        ],

        'inventory-sources' => [
            'index' => [
                'create-btn' => 'Create Inventory Source',
                'title'      => 'Inventory Sources',

                'datagrid' => [
                    'active'   => 'Active',
                    'code'     => 'Code',
                    'delete'   => 'Delete',
                    'edit'     => 'Edit',
                    'id'       => 'ID',
                    'inactive' => 'Inactive',
                    'name'     => 'Name',
                    'priority' => 'Priority',
                    'status'   => 'Status',
                ],
            ],

            'create' => [
                'add-title'      => 'Add Inventory Source',
                'address'        => 'Source Address',
                'back-btn'       => 'Back',
                'city'           => 'City',
                'code'           => 'Code',
                'contact-email'  => 'Email',
                'contact-fax'    => 'Fax',
                'contact-info'   => 'Contact Information',
                'contact-name'   => 'Name',
                'contact-number' => 'Contact Number',
                'country'        => 'Country',
                'description'    => 'Description',
                'general'        => 'General',
                'latitude'       => 'Latitude',
                'longitude'      => 'Longitude',
                'name'           => 'Name',
                'postcode'       => 'Postcode',
                'priority'       => 'Priority',
                'save-btn'       => 'Save Inventory Sources',
                'select-country' => 'Select Country',
                'select-state'   => 'Select State',
                'settings'       => 'Settings',
                'state'          => 'State',
                'status'         => 'Status',
                'street'         => 'Street',
                'title'          => 'Inventory Sources',
            ],

            'edit' => [
                'back-btn'       => 'Back',
                'city'           => 'City',
                'code'           => 'Code',
                'contact-email'  => 'Email',
                'contact-fax'    => 'Fax',
                'contact-info'   => 'Contact Information',
                'contact-name'   => 'Name',
                'contact-number' => 'Contact Number',
                'country'        => 'Country',
                'description'    => 'Description',
                'general'        => 'General',
                'latitude'       => 'Latitude',
                'longitude'      => 'Longitude',
                'name'           => 'Name',
                'postcode'       => 'Postcode',
                'priority'       => 'Priority',
                'save-btn'       => 'Save Inventory Sources',
                'select-country' => 'Select Country',
                'select-state'   => 'Select State',
                'settings'       => 'Settings',
                'source-address' => 'Source Address',
                'state'          => 'State',
                'status'         => 'Status',
                'street'         => 'Street',
                'title'          => 'Edit Inventory Sources',
            ],

            'create-success'    => 'Inventory Source Created Successfully',
            'delete-failed'     => 'Inventory Sources Delete Failed',
            'delete-success'    => 'Inventory Sources Deleted Successfully',
            'last-delete-error' => 'Last Inventory Sources Can Not Deleted',
            'update-success'    => 'Inventory Sources Updated Successfully',
        ],

        'taxes' => [
            'categories' => [
                'index' => [
                    'delete-warning' => 'Are You sure you want to delete ?',
                    'tax-category'   => 'Tax Category',
                    'title'          => 'Tax Categories',

                    'datagrid' => [
                        'actions' => 'Actions',
                        'code'    => 'Code',
                        'delete'  => 'Delete',
                        'edit'    => 'Edit',
                        'id'      => 'ID',
                        'name'    => 'Name',
                    ],

                    'create' => [
                        'add-tax-rates' => 'Add Tax Rates',
                        'code'          => 'Code',
                        'description'   => 'Description',
                        'empty-text'    => 'Tax Rates are not available please create new Tax Rates.',
                        'general'       => 'Tax Category',
                        'name'          => 'Name',
                        'save-btn'      => 'Save Tax Category',
                        'select'        => 'Select',
                        'tax-rates'     => 'Tax Rates',
                        'title'         => 'Create Tax Category',
                    ],

                    'edit' => [
                        'title' => 'Edit Tax Categories',
                    ],

                    'can-not-delete' => 'Tax Rates Assigned Categories cannot be deleted.',
                    'create-success' => 'New Tax Category Created.',
                    'delete-failed'  => 'Tax Category Deleted Failed',
                    'delete-success' => 'Tax Category Deleted Successfully.',
                    'update-success' => 'Tax Category Successfully Updated.',
                ],
            ],

            'rates' => [
                'index' => [
                    'button-title' => 'Create Tax Rate',
                    'tax-rate'     => 'Tax Rate',
                    'title'        => 'Tax Rates',

                    'datagrid' => [
                        'country'    => 'Country',
                        'delete'     => 'Delete',
                        'edit'       => 'Edit',
                        'id'         => 'ID',
                        'identifier' => 'Identifier',
                        'state'      => 'State',
                        'tax-rate'   => 'Tax Rate',
                        'zip-code'   => 'Zip Code',
                        'zip-from'   => 'Zip From',
                        'zip-to'     => 'Zip To',
                    ],
                ],

                'create' => [
                    'back-btn'       => 'Back',
                    'country'        => 'Country',
                    'general'        => 'General',
                    'identifier'     => 'Identifier',
                    'is-zip'         => 'Enable Zip Range',
                    'save-btn'       => 'Save Tax Rate',
                    'select-country' => 'Select Country',
                    'select-state'   => 'Select State',
                    'settings'       => 'Settings',
                    'state'          => 'State',
                    'tax-rate'       => 'Rate',
                    'title'          => 'Create Tax Rate',
                    'zip-code'       => 'Zip Code',
                    'zip-from'       => 'Zip From',
                    'zip-to'         => 'Zip To',
                ],

                'edit' => [
                    'back-btn'       => 'Back',
                    'country'        => 'Country',
                    'identifier'     => 'Identifier',
                    'save-btn'       => 'Save Tax Rate',
                    'select-country' => 'Select Country',
                    'select-state'   => 'Select State',
                    'settings'       => 'Settings',
                    'state'          => 'State',
                    'tax-rate'       => 'Rate',
                    'title'          => 'Edit Tax Rate',
                    'zip-code'       => 'Zip Code',
                    'zip-from'       => 'Zip From',
                    'zip-to'         => 'Zip To',
                ],

                'create-success' => 'Tax rate created successfully.',
                'delete-failed'  => 'Tax rate deleted failed',
                'delete-success' => 'Tax rate deleted successfully',
                'update-success' => 'Tax Rate Update Successfully',
            ],
        ],

        'channels' => [
            'index' => [
                'create-btn'        => 'Create Channel',
                'delete-failed'     => 'Channel Delete Failed',
                'delete-success'    => 'Channel deleted successfully.',
                'last-delete-error' => 'Last Channel deleted failed.',
                'title'             => 'Channels',

                'datagrid' => [
                    'code'      => 'Code',
                    'delete'    => 'Delete',
                    'edit'      => 'Edit',
                    'host-name' => 'Host Name',
                    'id'        => 'ID',
                    'name'      => 'Name',
                ],
            ],

            'create' => [
                'allowed-ips'             => 'Allowed IPs',
                'cancel'                  => 'Back',
                'code'                    => 'Code',
                'create-success'          => 'Channel created successfully.',
                'currencies'              => 'Currencies',
                'currencies-and-locales'  => 'Currencies and Locales',
                'default-currency'        => 'Default Currency',
                'default-locale'          => 'Default Locale',
                'description'             => 'Description',
                'design'                  => 'Design',
                'favicon'                 => 'Favicon',
                'favicon-size'            => 'Image resolution should be like 16px X 16px',
                'general'                 => 'General',
                'hostname'                => 'Hostname',
                'hostname-placeholder'    => 'https://www.example.com (Don\'t add slash in the end.)',
                'inventory-sources'       => 'Inventory Sources',
                'last-delete-error'       => 'At least one Channel is required.',
                'locales'                 => 'Locales',
                'logo'                    => 'Logo',
                'logo-size'               => 'Image resolution should be like 192px X 50px',
                'maintenance-mode-text'   => 'Message',
                'name'                    => 'Name',
                'root-category'           => 'Root Category',
                'save-btn'                => 'Save Channel',
                'select-default-currency' => 'Select Default Currency',
                'select-default-locale'   => 'Select Default Locale',
                'select-root-category'    => 'Select Root Category',
                'select-theme'            => 'Select Theme',
                'seo'                     => 'Home page SEO',
                'seo-description'         => 'Meta description',
                'seo-keywords'            => 'Meta keywords',
                'seo-title'               => 'Meta title',
                'settings'                => 'Settings',
                'status'                  => 'Status',
                'theme'                   => 'Theme',
                'title'                   => 'Create Channel',
            ],

            'edit' => [
                'allowed-ips'            => 'Allowed IPs',
                'back-btn'               => 'Back',
                'code'                   => 'Code',
                'currencies'             => 'Currencies',
                'currencies-and-locales' => 'Currencies and Locales',
                'default-currency'       => 'Default Currency',
                'default-locale'         => 'Default Locale',
                'description'            => 'Description',
                'design'                 => 'Design',
                'favicon'                => 'Favicon',
                'favicon-size'           => 'Image resolution should be like 16px X 16px',
                'general'                => 'General',
                'hostname'               => 'Hostname',
                'hostname-placeholder'   => 'https://www.example.com (Don\'t add slash in the end.)',
                'inventory-sources'      => 'Inventory Sources',
                'last-delete-error'      => 'At least one Channel is required.',
                'locales'                => 'Locales',
                'logo'                   => 'Logo',
                'logo-size'              => 'Image resolution should be like 192px X 50px',
                'maintenance-mode'       => 'Maintenance Mode',
                'maintenance-mode-text'  => 'Message',
                'name'                   => 'Name',
                'root-category'          => 'Root Category',
                'save-btn'               => 'Save Channel',
                'seo'                    => 'Home page SEO',
                'seo-description'        => 'Meta description',
                'seo-keywords'           => 'Meta keywords',
                'seo-title'              => 'Meta title',
                'status'                 => 'Status',
                'theme'                  => 'Theme',
                'title'                  => 'Edit Channel',
                'update-success'         => 'Update Channel Successfully',
            ],
        ],

        'users' => [
            'index' => [
                'admin' => 'Admin',
                'title' => 'Users',
                'user'  => 'User',

                'create' => [
                    'confirm-password'  => 'Confirm Password',
                    'email'             => 'Email',
                    'name'              => 'Name',
                    'password'          => 'Password',
                    'role'              => 'Role',
                    'save-btn'          => 'Save User',
                    'status'            => 'Status',
                    'title'             => 'Create User',
                    'upload-image-info' => 'Upload a Profile Image (110px X 110px) in PNG or JPG Format',
                ],

                'datagrid' => [
                    'actions'  => 'Actions',
                    'active'   => 'Active',
                    'delete'   => 'Delete',
                    'edit'     => 'Edit',
                    'email'    => 'Email',
                    'id'       => 'ID',
                    'inactive' => 'Inactive',
                    'name'     => 'Name',
                    'role'     => 'Role',
                    'status'   => 'Status',
                ],

                'edit' => [
                    'title' => 'Edit User',
                ],
            ],

            'edit' => [
                'back-btn'         => 'Back',
                'confirm-password' => 'Confirm Password',
                'email'            => 'Email',
                'general'          => 'General',
                'name'             => 'Name',
                'password'         => 'Password',
                'role'             => 'Role',
                'save-btn'         => 'Save User',
                'status'           => 'Status',
                'title'            => 'Edit User',
            ],

            'activate-warning'   => 'Your account is yet to be activated, please contact administrator.',
            'cannot-change'      => 'User cannot be changed',
            'create-success'     => 'User created successfully.',
            'delete-failed'      => 'User deleted failed.',
            'delete-success'     => 'User deleted successfully.',
            'delete-warning'     => 'Are you sure, you want to perform this action?',
            'incorrect-password' => 'Incorrect password',
            'last-delete-error'  => 'Last User delete failed',
            'login-error'        => 'Please check your credentials and try again.',
            'update-success'     => 'User updated successfully.',
        ],

        'roles' => [
            'index' => [
                'create-btn' => 'Create Role',
                'title'      => 'Roles',

                'datagrid' => [
                    'all'             => 'All',
                    'custom'          => 'Custom',
                    'delete'          => 'Delete',
                    'edit'            => 'Edit',
                    'id'              => 'Id',
                    'name'            => 'Name',
                    'permission-type' => 'Permission Type',
                ],
            ],

            'create' => [
                'access-control' => 'Access Control',
                'all'            => 'All',
                'back-btn'       => 'Back',
                'custom'         => 'Custom',
                'description'    => 'Description',
                'general'        => 'General',
                'name'           => 'Name',
                'permissions'    => 'Permissions',
                'save-btn'       => 'Save Role',
                'title'          => 'Create Role',
            ],

            'edit' => [
                'access-control' => 'Access Control',
                'all'            => 'All',
                'back-btn'       => 'Back',
                'custom'         => 'Custom',
                'description'    => 'Description',
                'general'        => 'General',
                'name'           => 'Name',
                'permissions'    => 'Permissions',
                'save-btn'       => 'Save Role',
                'title'          => 'Edit Role',
            ],

            'being-used'        => 'Role is already used in Admin User',
            'create-success'    => 'Roles Created Successfully',
            'delete-failed'     => 'Roles is deleted failed',
            'delete-success'    => 'Roles is deleted successfully',
            'last-delete-error' => 'Last Roles can not be deleted',
            'update-success'    => 'Roles is updated successfully',
        ],

        'themes' => [
            'index' => [
                'create-btn' => 'Create Theme',
                'title'      => 'Themes',

                'datagrid' => [
                    'active'        => 'Active',
                    'channel_name'  => 'Channel Name',
                    'change-status' => 'Change status',
                    'delete'        => 'Delete',
                    'id'            => 'Id',
                    'inactive'      => 'Inactive',
                    'name'          => 'Name',
                    'sort-order'    => 'Sort Order',
                    'status'        => 'Status',
                    'theme'         => 'Theme',
                    'type'          => 'Type',
                    'view'          => 'View',
                ],
            ],

            'create' => [
                'name'       => 'Name',
                'save-btn'   => 'Save Theme',
                'sort-order' => 'Sort Order',
                'themes'     => 'Themes',
                'title'      => 'Create Theme',

                'type' => [
                    'category-carousel' => 'Category Carousel',
                    'footer-links'      => 'Footer Links',
                    'image-carousel'    => 'Image Carousel',
                    'product-carousel'  => 'Product Carousel',
                    'services-content'  => 'Services Content',
                    'static-content'    => 'Static Content',
                    'title'             => 'Type',
                ],
            ],

            'edit' => [
                'active'                        => 'Active',
                'add-filter-btn'                => 'Add Filter',
                'add-footer-link-btn'           => 'Add Footer Link',
                'add-image-btn'                 => 'Add Image',
                'add-link'                      => 'Add Link',
                'asc'                           => 'Asc',
                'back'                          => 'Back',
                'category-carousel'             => 'Category Carousel',
                'category-carousel-description' => 'Display dynamic categories attractively using a responsive category carousel.',
                'channels'                      => 'Channels',
                'column'                        => 'Column',
                'create-filter'                 => 'Create Filter',
                'css'                           => 'CSS',
                'delete'                        => 'Delete',
                'desc'                          => 'Desc',
                'edit'                          => 'Edit',
                'featured'                      => 'Featured',
                'filter-title'                  => 'Title',
                'filters'                       => 'Filters',
                'footer-link'                   => 'Footer Links',
                'footer-link-description'       => 'Navigate via footer links for seamless website exploration and information.',
                'footer-link-form-title'        => 'Footer Link',
                'footer-title'                  => 'Title',
                'general'                       => 'General',
                'html'                          => 'HTML',
                'image'                         => 'Image',
                'image-size'                    => 'Image resolution should be (1920px X 700px)',
                'image-title'                   => 'Image Title',
                'image-upload-message'          => 'Only images (.jpeg, .jpg, .png, .webp, ..) are allowed.',
                'inactive'                      => 'Inactive',
                'key'                           => 'Key: :key',
                'key-input'                     => 'Key',
                'limit'                         => 'Limit',
                'link'                          => 'Link',
                'name'                          => 'Name',
                'new'                           => 'New',
                'no'                            => 'No',
                'parent-id'                     => 'Parent ID',
                'category-id'                   => 'Category ID',
                'preview'                       => 'Preview',
                'product-carousel'              => 'Product Carousel',
                'product-carousel-description'  => 'Showcase products elegantly with a dynamic and responsive product carousel.',
                'save-btn'                      => 'Save',
                'select'                        => 'Select',
                'slider'                        => 'Slider',
                'slider-add-btn'                => 'Add Slider',
                'slider-description'            => 'Slider related theme customization.',
                'slider-image'                  => 'Slider Image',
                'slider-required'               => 'Slider field is Required.',
                'sort'                          => 'Sort',
                'sort-order'                    => 'Sort Order',
                'static-content'                => 'Static Content',
                'static-content-description'    => 'Improve engagement with concise, informative static content for your audience.',
                'status'                        => 'Status',
                'themes'                        => 'Themes',
                'title'                         => 'Edit Theme',
                'update-slider'                 => 'Update Slider',
                'url'                           => 'URL',
                'value'                         => 'Value: :value',
                'value-input'                   => 'Value',

                'services-content' => [
                    'add-btn'            => 'Add Services',
                    'channels'           => 'Channels',
                    'delete'             => 'Delete',
                    'description'        => 'Description',
                    'general'            => 'General',
                    'name'               => 'Name',
                    'save-btn'           => 'Save',
                    'service-icon'       => 'Service Icon',
                    'service-icon-class' => 'Service Icon Class',
                    'service-info'       => 'Service related theme customization.',
                    'services'           => 'Services',
                    'sort-order'         => 'Sort Order',
                    'status'             => 'Status',
                    'title'              => 'Title',
                    'update-service'     => 'Update Services',
                ],
                'yes'                    => 'Yes',
            ],

            'create-success' => 'Theme created successfully',
            'delete-success' => 'Theme deleted successfully',
            'update-success' => 'Theme updated successfully',
        ],
    ],

    'reporting' => [
        'sales' => [
            'index' => [
                'abandoned-carts'               => 'Abandoned Carts',
                'abandoned-products'            => 'Abandoned Products',
                'abandoned-rate'                => 'Abandoned Rate',
                'abandoned-revenue'             => 'Abandoned Revenue',
                'added-to-cart-info'            => 'Only :progress visitors added products to cart',
                'added-to-cart'                 => 'Added to Cart',
                'all-channels'                  => 'All Channels',
                'average-order-value-over-time' => 'Average Order Value Over Time',
                'average-sales'                 => 'Average Order Value',
                'count'                         => 'Count',
                'end-date'                      => 'End Date',
                'id'                            => 'Id',
                'interval'                      => 'Interval',
                'name'                          => 'Name',
                'orders'                        => 'Orders',
                'orders-over-time'              => 'Orders Over Time',
                'payment-method'                => 'Payment Method',
                'product-views'                 => 'Product Views',
                'product-views-info'            => 'Only :progress visitors view products',
                'purchase-funnel'               => 'Purchase Funnel',
                'purchased'                     => 'Purchased',
                'purchased-info'                => 'Only :progress visitors do the purchasing',
                'refunds'                       => 'Refunds',
                'refunds-over-time'             => 'Refunds Over Time',
                'sales-over-time'               => 'Sales Over Time',
                'shipping-collected'            => 'Shipping Collected',
                'shipping-collected-over-time'  => 'Shipping Collected Over Time',
                'start-date'                    => 'Start Date',
                'tax-collected'                 => 'Tax Collected',
                'tax-collected-over-time'       => 'Tax Collected Over Time',
                'title'                         => 'Sales',
                'top-payment-methods'           => 'Top Payment Methods',
                'top-shipping-methods'          => 'Top Shipping Methods',
                'top-tax-categories'            => 'Top Tax Categories',
                'total'                         => 'Total',
                'total-orders'                  => 'Total Orders',
                'total-sales'                   => 'Total Sales',
                'total-visits'                  => 'Total visits',
                'total-visits-info'             => 'Total visitors on store',
                'view-details'                  => 'View Details',
            ],
        ],

        'customers' => [
            'index' => [
                'all-channels'                => 'All Channels',
                'count'                       => 'Count',
                'customers'                   => 'Customers',
                'customers-over-time'         => 'Customers Over Time',
                'customers-traffic'           => 'Customers Traffic',
                'customers-with-most-orders'  => 'Customers With Most Orders',
                'customers-with-most-reviews' => 'Customers With Most Reviews',
                'customers-with-most-sales'   => 'Customers With Most Sales',
                'email'                       => 'Email',
                'end-date'                    => 'End Date',
                'id'                          => 'Id',
                'interval'                    => 'Interval',
                'name'                        => 'Name',
                'orders'                      => 'Orders',
                'reviews'                     => 'Reviews',
                'start-date'                  => 'Start Date',
                'title'                       => 'Customers',
                'top-customer-groups'         => 'Top Customer Groups',
                'total'                       => 'Total',
                'total-customers'             => 'Total Customers',
                'total-visitors'              => 'Total Visitors',
                'traffic-over-week'           => 'Traffic Over Week',
                'unique-visitors'             => 'Unique Visitors',
                'view-details'                => 'View Details',
            ],
        ],

        'products' => [
            'index' => [
                'all-channels'                     => 'All Channels',
                'channel'                          => 'Channel',
                'end-date'                         => 'End Date',
                'id'                               => 'Id',
                'interval'                         => 'Interval',
                'last-search-terms'                => 'Last Search Terms',
                'locale'                           => 'Locale',
                'name'                             => 'Name',
                'orders'                           => 'Orders',
                'price'                            => 'Price',
                'products-added-over-time'         => 'Products Added Over Time',
                'products-with-most-reviews'       => 'Products With Most Reviews',
                'products-with-most-visits'        => 'Products With Most Visits',
                'quantities'                       => 'Quantities',
                'quantities-sold-over-time'        => 'Quantities Sold Over Time',
                'results'                          => 'Results',
                'revenue'                          => 'Revenue',
                'reviews'                          => 'Reviews',
                'search-term'                      => 'Search Term',
                'start-date'                       => 'Start Date',
                'title'                            => 'Products',
                'top-search-terms'                 => 'Top Search Terms',
                'top-selling-products-by-quantity' => 'Top Selling Products By Quantity',
                'top-selling-products-by-revenue'  => 'Top Selling Products By Revenue',
                'total'                            => 'Total',
                'total-products-added-to-wishlist' => 'Products Added To Wishlist',
                'total-sold-quantities'            => 'Sold Products Quantity',
                'uses'                             => 'Uses',
                'view-details'                     => 'View Details',
                'visits'                           => 'Visits',
            ],
        ],

        'view' => [
            'all-channels'  => 'All Channels',
            'back-btn'      => 'Back',
            'day'           => 'Day',
            'end-date'      => 'End Date',
            'export-csv'    => 'Export CSV',
            'export-xls'    => 'Export XLS',
            'month'         => 'Month',
            'not-available' => 'No Records Available.',
            'start-date'    => 'Start Date',
            'year'          => 'Year',
        ],

        'empty' => [
            'info'  => 'No data available for selected interval',
            'title' => 'No Data Available',
        ],
    ],

    'configuration' => [
        'index' => [
            'back-btn'                     => 'Back',
            'delete'                       => 'Delete',
            'enable-at-least-one-payment'  => 'Enable at least one payment method.',
            'enable-at-least-one-shipping' => 'Enable at least one shipping method.',
            'no-result-found'              => 'No result found',
            'save-btn'                     => 'Save Configuration',
            'save-message'                 => 'Configuration saved successfully',
            'search'                       => 'Search',
            'select-country'               => 'Select Country',
            'select-state'                 => 'Select State',
            'title'                        => 'Configuration',

            'general' => [
                'info'  => 'Set units options.',
                'title' => 'General',

                'general' => [
                    'info'  => 'Set units options and enable or disable breadcrumbs.',
                    'title' => 'General',

                    'unit-options' => [
                        'info'        => 'Set units options.',
                        'title'       => 'Unit Options',
                        'title-info'  => 'Configure the weight in pounds (lbs) or kilograms (kgs).',
                        'weight-unit' => 'Weight Unit',
                    ],

                    'breadcrumbs' => [
                        'shop'       => 'Shop Breadcrumbs',
                        'title'      => 'Breadcrumbs',
                        'title-info' => 'Enable or disable breadcrumbs navigation in the shop.',
                    ],
                ],

                'content' => [
                    'info'  => 'Set header offer title and custom scripts.',
                    'title' => 'Content',

                    'header-offer' => [
                        'title'             => 'Header Offer Title',
                        'title-info'        => 'Configure Header Offer Title with offer title, redirection title, and redirection link.',
                        'offer-title'       => 'Offer Title',
                        'redirection-title' => 'Redirection Title',
                        'redirection-link'  => 'Redirection Link',
                    ],

                    'custom-scripts' => [
                        'custom-css'        => 'Custom CSS',
                        'custom-javascript' => 'Custom Javascript',
                        'title'             => 'Custom Scripts',
                        'title-info'        => 'Custom scripts are personalized pieces of code created to add specific functions or features to software, enhancing its capabilities uniquely.',
                    ],
                ],

                'design' => [
                    'info'  => 'Set logo and favicon icon for admin panel.',
                    'title' => 'Design',

                    'admin-logo' => [
                        'favicon'    => 'Favicon',
                        'logo-image' => 'Logo Image',
                        'title'      => 'Admin Logo',
                        'title-info' => 'Configure logo and favicon images for your website\'s front end for better branding and recognition.',
                    ],
                ],

                'magic-ai' => [
                    'info'  => 'Set Magic AI options and allow some options to automate the creation of content.',
                    'title' => 'Magic AI',

                    'settings' => [
                        'api-key'        => 'API Key',
                        'enabled'        => 'Enabled',
                        'llm-api-domain' => 'LLM API Domain',
                        'organization'   => 'Organization',
                        'title'          => 'General Settings',
                        'title-info'     => 'Enhance your experience with the Magic AI feature by entering your exclusive API Key and indicating the pertinent Organization for effortless integration. Seize command over your OpenAI credentials and customize the settings according to your specific needs.',
                    ],

                    'content-generation' => [
                        'category-description-prompt'      => 'Category Description Prompt',
                        'cms-page-content-prompt'          => 'CMS Page Content Prompt',
                        'enabled'                          => 'Enabled',
                        'product-description-prompt'       => 'Product Description Prompt',
                        'product-short-description-prompt' => 'Product Short Description Prompt',
                        'title'                            => 'Content Generation',
                        'title-info'                       => 'This feature will enable the Magic AI for every WYSIWYG editor, where you want to mange content using AI.<br/><br/>When Enable, go to any editor to generate content.',
                    ],

                    'image-generation' => [
                        'enabled'    => 'Enabled',
                        'title'      => 'Image Generation',
                        'title-info' => 'This feature will enable the Magic AI for every image upload, where you want to generate images using DALL-E.<br/><br/>When Enable, go to any image upload to generate image.',
                    ],

                    'review-translation' => [
                        'deepseek-r1-8b'      => 'DeepSeek R1 (8b)',
                        'enabled'             => 'Enabled',
                        'gemini-2-0-flash'    => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'         => 'OpenAI gpt-4 Turbo',
                        'gpt-4o'              => 'OpenAI gpt-4o',
                        'gpt-4o-mini'         => 'OpenAI gpt-4o mini',
                        'llama-groq'          => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'         => 'Llama 3.1 (8B)',
                        'llama3-2-1b'         => 'Llama 3.2 (1B)',
                        'llama3-2-3b'         => 'Llama 3.2 (3B)',
                        'llama3-8b'           => 'Llama 3 (8B)',
                        'llava-7b'            => 'Llava (7b)',
                        'mistral-7b'          => 'Mistral (7b)',
                        'model'               => 'Model',
                        'orca-mini'           => 'Orca Mini',
                        'phi3-5'              => 'Phi 3.5',
                        'qwen2-5-0-5b'        => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'        => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'         => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'          => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'          => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'      => 'Starling-lm (7b)',
                        'title'               => 'Review Translation',
                        'title-info'          => 'Provide option to customer or visitor to translate customer review into english.<br/><br/>When enable, go to review and you will find the button ‘Translate to English’ if you review other then English.',
                        'vicuna-13b'          => 'Vicuna (13b)',
                        'vicuna-7b'           => 'Vicuna (7b)',
                    ],

                    'checkout-message' => [
                        'deepseek-r1-8b'      => 'DeepSeek R1 (8b)',
                        'enabled'             => 'Enabled',
                        'gemini-2-0-flash'    => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'         => 'OpenAI gpt 4 Turbo',
                        'gpt-4o'              => 'OpenAI gpt-4o',
                        'gpt-4o-mini'         => 'OpenAI gpt-4o mini',
                        'llama-groq'          => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'         => 'Llama 3.1 (8B)',
                        'llama3-2-1b'         => 'Llama 3.2 (1B)',
                        'llama3-2-3b'         => 'Llama 3.2 (3B)',
                        'llama3-8b'           => 'Llama 3 (8B)',
                        'llava-7b'            => 'Llava (7b)',
                        'mistral-7b'          => 'Mistral (7b)',
                        'model'               => 'Model',
                        'orca-mini'           => 'Orca Mini',
                        'phi3-5'              => 'Phi 3.5',
                        'prompt'              => 'Prompt',
                        'qwen2-5-0-5b'        => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'        => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'         => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'          => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'          => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'      => 'Starling-lm (7b)',
                        'title'               => 'Personalized Checkout Message',
                        'title-info'          => 'Craft a personalized checkout message for customers on the Thank-You page, tailoring the content to resonate with individual preferences and enhancing the overall post-purchase experience.',
                        'vicuna'              => 'Vicuna',
                        'vicuna-13b'          => 'Vicuna (13b)',
                        'vicuna-7b'           => 'Vicuna (7b)',
                    ],
                ],

                'gdpr' => [
                    'title' => 'GDPR',
                    'info'  => 'GDPR Compliance Settings',

                    'settings' => [
                        'title'   => 'GDPR Compliance Settings',
                        'info'    => 'Manage GDPR compliance settings, including data privacy agreement. Enable or disable GDPR features as required',
                        'enabled' => 'Enable GDPR',
                    ],

                    'agreement' => [
                        'title'          => 'GDPR Agreement',
                        'info'           => 'Manage customer consent under GDPR regulations. Enable agreement requirements for data collection and processing.',
                        'enable'         => 'Enable Customer Agreement',
                        'checkbox-label' => 'Agreement Checkbox Label',
                        'content'        => 'Agreement Content',
                    ],

                    'cookie' => [
                        'bottom-left'  => 'Bottom Left',
                        'bottom-right' => 'Bottom Right',
                        'center'       => 'Center',
                        'description'  => 'Description',
                        'enable'       => 'Enable Cookie Notice',
                        'identifier'   => 'Static Block Identifier',
                        'info'         => 'Configure cookie consent settings to inform users about data collection and ensure compliance with privacy regulations.',
                        'position'     => 'Cookie Block Display Position',
                        'title'        => 'Cookie Notice Settings',
                        'top-left'     => 'Top Left',
                        'top-right'    => 'Top Right',
                    ],

                    'cookie-consent' => [
                        'title'                  => 'Manage Your Cookie Preferences',
                        'info'                   => 'Control how your data is used by selecting your preferred cookie settings. Adjust permissions for different types of cookies.',
                        'strictly-necessary'     => 'Strictly Necessary',
                        'basic-interaction'      => 'Basic Interactions & Functionalities',
                        'experience-enhancement' => 'Experience Enhancements',
                        'measurement'            => 'Measurements',
                        'targeting-advertising'  => 'Targeting & Advertising',
                    ],
                ],

                'sitemap' => [
                    'info'  => 'Set sitemap options.',
                    'title' => 'Sitemap',

                    'settings' => [
                        'enabled' => 'Enabled',
                        'info'    => 'Enable or disable the sitemap for your website to improve search engine optimization and enhance user experience.',
                        'title'   => 'Settings',
                    ],

                    'file-limits' => [
                        'info'             => 'Set file limits options.',
                        'max-file-size'    => 'Maximum file size',
                        'max-url-per-file' => 'Maximum no. of URLs per file',
                        'title'            => 'File Limits',
                    ],
                ],
            ],

            'catalog' => [
                'info'  => 'Catalog',
                'title' => 'Catalog',

                'products' => [
                    'info'  => 'Product view page, cart view page, store front, review and attribute social share.',
                    'title' => 'Products',

                    'settings' => [
                        'compare-options'     => 'Compare options',
                        'image-search-option' => 'Image Search Option',
                        'title'               => 'Settings',
                        'title-info'          => 'Settings refer to configurable choices that control how a system, application, or device behaves, tailored to user preferences and requirements.',
                        'wishlist-options'    => 'Wishlist options',
                    ],

                    'search' => [
                        'admin-mode'            => 'Admin Search Mode',
                        'admin-mode-info'       => 'Mega Search, Datagrid, and other search functionalities in the admin panel will be based on the selected search engine.',
                        'database'              => 'Database',
                        'elastic'               => 'Elastic Search',
                        'max-query-length'      => 'Maximum query length',
                        'max-query-length-info' => 'Set maximum query length for search queries.',
                        'min-query-length'      => 'Minimum query length',
                        'min-query-length-info' => 'Set minimum query length for search queries.',
                        'search-engine'         => 'Search Engine',
                        'storefront-mode'       => 'Storefront Search Mode',
                        'storefront-mode-info'  => 'Search functionality on the storefront will be based on the selected search engine including category page, search page, and other search functionalities.',
                        'title'                 => 'Search',
                        'title-info'            => 'To set up the search engine for product searches, you can choose between a database and Elasticsearch based on your requirements. If you have a large number of products, Elasticsearch is recommended.',
                    ],

                    'guest-checkout' => [
                        'allow-guest-checkout'      => 'Allow Guest Checkout',
                        'allow-guest-checkout-hint' => 'Hint: If turned on, this option can be configured for each product specifically.',
                        'title'                     => 'Guest Checkout',
                        'title-info'                => 'Guest checkout allows customers to buy products without creating an account, streamlining the purchase process for convenience and faster transactions.',
                    ],

                    'product-view-page' => [
                        'allow-no-of-related-products'  => 'Allowed number of Related Products',
                        'allow-no-of-up-sells-products' => 'Allowed number of Up-Sell Products',
                        'title'                         => 'Product view page configuration',
                        'title-info'                    => 'Product view page configuration entails adjusting the layout and elements on a product\'s display page, enhancing user experience and information presentation.',
                    ],

                    'cart-view-page' => [
                        'allow-no-of-cross-sells-products' => 'Allowed number of Cross-Sell Products',
                        'title'                            => 'Cart view page configuration',
                        'title-info'                       => 'Cart view page configuration involves arranging items, details, and options on the shopping cart page, improving user interaction and purchase flow.',
                    ],

                    'storefront' => [
                        'buy-now-button-display' => 'Allow customers to directly buy products',
                        'cheapest-first'         => 'Cheapest First',
                        'comma-separated'        => 'Comma Separated',
                        'default-list-mode'      => 'Default List Mode',
                        'expensive-first'        => 'Expensive First',
                        'from-a-z'               => 'From A-Z',
                        'from-z-a'               => 'From Z-A',
                        'grid'                   => 'Grid',
                        'latest-first'           => 'Newest First',
                        'list'                   => 'List',
                        'oldest-first'           => 'Oldest First',
                        'products-per-page'      => 'Products Per Page',
                        'sort-by'                => 'Sort By',
                        'title'                  => 'Storefront',
                        'title-info'             => 'Storefront is the customer-facing interface of an online shop, showcasing products, categories, and navigation for a seamless shopping experience.',
                    ],

                    'small-image' => [
                        'height'      => 'Height',
                        'placeholder' => 'Small Image Placeholder',
                        'title'       => 'Small Image',
                        'title-info'  => 'Storefront is the customer-facing interface of an online shop, showcasing products, categories, and navigation for a seamless shopping experience.',
                        'width'       => 'Width',
                    ],

                    'medium-image' => [
                        'height'      => 'Height',
                        'placeholder' => 'Medium Image Placeholder',
                        'title'       => 'Medium Image',
                        'title-info'  => 'Medium image refers to a moderate-sized picture that offers a balance between detail and screen space, commonly used for visuals.',
                        'width'       => 'Width',
                    ],

                    'large-image' => [
                        'height'      => 'Height',
                        'placeholder' => 'Large Image Placeholder',
                        'title'       => 'Large image',
                        'title-info'  => 'Large image represents a high-resolution picture providing enhanced detail and visual impact, often used for showcasing products or graphics.',
                        'width'       => 'Width',
                    ],

                    'review' => [
                        'allow-customer-review'   => 'Allow Customer Review',
                        'allow-guest-review'      => 'Allow Guest Review',
                        'censoring-reviewer-name' => 'Censoring Reviewer Name',
                        'display-review-count'    => 'Display the review count for ratings.',
                        'display-star-count'      => 'Display the star count in ratings.',
                        'summary'                 => 'Summary',
                        'title'                   => 'Review',
                        'title-info'              => 'Evaluation or assessment of something, often involving opinions and feedback.',
                    ],

                    'attribute' => [
                        'file-upload-size'  => 'Allowed File Upload Size (in Kb)',
                        'image-upload-size' => 'Allowed Image Upload Size (in Kb)',
                        'title'             => 'Attribute',
                        'title-info'        => 'Characteristic or property that defines an object, influencing its behavior, appearance, or function.',
                    ],

                    'social-share' => [
                        'title-info'                  => 'Configure social sharing settings to enable product sharing on Instagram, Twitter, WhatsApp, Facebook, Pinterest, LinkedIn, and via email.',
                        'title'                       => 'Social Share',
                        'share-message'               => 'Share Message',
                        'share'                       => 'Share',
                        'enable-social-share'         => 'Enable Social Share?',
                        'enable-share-whatsapp-info'  => 'What\'s App share link just will appear to mobile devices.',
                        'enable-share-whatsapp'       => 'Enable Share in What\'s App?',
                        'enable-share-twitter'        => 'Enable Share in Twitter?',
                        'enable-share-pinterest'      => 'Enable Share in Pinterest?',
                        'enable-share-linkedin'       => 'Enable Share in Linkedin?',
                        'enable-share-facebook'       => 'Enable Share in Facebook?',
                        'enable-share-email'          => 'Enable Share in Email?',
                    ],
                ],

                'rich-snippets' => [
                    'info'  => 'Set products and categories.',
                    'title' => 'Rich Snippets',

                    'products' => [
                        'enable'          => 'Enable',
                        'show-categories' => 'Show Categories',
                        'show-images'     => 'Show Images',
                        'show-offers'     => 'Show Offers',
                        'show-ratings'    => 'Show Ratings',
                        'show-reviews'    => 'Show Reviews',
                        'show-sku'        => 'Show SKU',
                        'show-weight'     => 'Show Weight',
                        'title'           => 'Products',
                        'title-info'      => 'Configure product settings including with SKU, weight, Categories, Images, Reviews, Ratings, Offers and etc.',
                    ],

                    'categories' => [
                        'enable'                  => 'Enable',
                        'show-search-input-field' => 'Show Search Input Field',
                        'title'                   => 'Categories',
                        'title-info'              => '"Categories" refer to groups or classifications that help organize and group similar products or items together for easier browsing and navigation.',
                    ],
                ],

                'inventory' => [
                    'title'      => 'Inventory',
                    'title-info' => 'Configure inventory settings to allow back orders and define the out-of-stock threshold.',

                    'product-stock-options' => [
                        'allow-back-orders'       => 'Allow Back Orders',
                        'max-qty-allowed-in-cart' => 'Maximum Qty Allowed in Shopping Cart',
                        'min-qty-allowed-in-cart' => 'Minimum Qty Allowed in Shopping Cart',
                        'out-of-stock-threshold'  => 'Out-of-Stock Threshold',
                        'title'                   => 'Product Stock Option',
                        'info'                    => 'Configure product stock options to allow back orders, set minimum and maximum cart quantities, and define out-of-stock thresholds.',
                    ],
                ],
            ],

            'customer' => [
                'info'  => 'Customer',
                'title' => 'Customer',

                'address' => [
                    'info'  => 'Set country, state, zip and lines in a street address.',
                    'title' => 'Address',

                    'requirements' => [
                        'city'       => 'City',
                        'country'    => 'Country',
                        'state'      => 'State',
                        'title'      => 'Requirements',
                        'title-info' => 'Requirements are the conditions, features, or specifications necessary for something to be fulfilled, achieved, or met successfully.',
                        'zip'        => 'Zip',
                    ],

                    'information' => [
                        'street-lines' => 'Lines in a Street Address',
                        'title'        => 'Information',
                        'title-info'   => '"Lines in a street address" refer to individual segments of an address, often separated by commas, providing location information such as house number, street, city, and more.',
                    ],
                ],

                'captcha' => [
                    'info'  => 'Set site key, secret key and status.',
                    'title' => 'Google Captcha',

                    'credentials' => [
                        'secret-key' => 'Secret Key',
                        'site-key'   => 'Site Key',
                        'status'     => 'Status',
                        'title'      => 'Credentials',
                        'title-info' => '"Sitemap: Website layout map for search engines. Secret key: Secure code for data encryption, authentication, or API access protection."',
                    ],

                    'validations' => [
                        'captcha'  => 'Something went wrong! Please try again.',
                        'required' => 'Please select CAPTCHA',
                    ],
                ],

                'settings' => [
                    'settings-info' => 'Set wishlist, login redirection, newsletter subscriptions, default group option , email verifications and social login.',
                    'title'         => 'Settings',

                    'login-as-customer' => [
                        'allow-option' => 'Allow Login as Customer',
                        'title'        => 'Login As Customer',
                        'title-info'   => 'Enable "Login as Customer" functionality.',
                    ],

                    'wishlist' => [
                        'allow-option' => 'Allow Wishlist option',
                        'title'        => 'Wishlist',
                        'title-info'   => 'Enable or disable the wishlist option.',
                    ],

                    'login-options' => [
                        'account'          => 'Account',
                        'home'             => 'Home',
                        'redirect-to-page' => 'Redirect Customer to the selected page',
                        'title'            => 'Login Options',
                        'title-info'       => 'Configure login options to determine the redirect page for customers after login.',
                    ],

                    'create-new-account-option' => [
                        'news-letter'      => 'Allow NewsLetter',
                        'news-letter-info' => 'Enable newsletter subscription option on the sign-up page.',
                        'title'            => 'Create New Account Options',
                        'title-info'       => 'Set options for new accounts, including assigning a default customer group and enabling the newsletter subscription option during sign-up.',

                        'default-group' => [
                            'general'    => 'General',
                            'guest'      => 'Guest',
                            'title'      => 'Default Group',
                            'title-info' => 'Assign a specific customer group as the default for new customers.',
                            'wholesale'  => 'Wholesale',
                        ],
                    ],

                    'newsletter' => [
                        'subscription' => 'Allow Newsletter Subscription',
                        'title'        => 'Newsletter Subscription',
                        'title-info'   => '"Newsletter information" contains updates, offers, or content shared regularly through emails to subscribers, keeping them informed and engaged.',
                    ],

                    'email' => [
                        'email-verification' => 'Allow Email Verification',
                        'title'              => 'Email Verification',
                        'title-info'         => '"Email verification" confirms the authenticity of an email address, often by sending a confirmation link, enhancing account security and communication reliability.',
                    ],

                    'social-login' => [
                        'enable-facebook'   => 'Enable Facebook',
                        'enable-github'     => 'Enable Github',
                        'enable-google'     => 'Enable Google',
                        'enable-linkedin'   => 'Enable LinkedIn',
                        'enable-twitter'    => 'Enable Twitter',
                        'social-login'      => 'Social Login',
                        'social-login-info' => '"Social login" enables users to access websites using their social media accounts, streamlining registration and login processes for convenience.',
                    ],
                ],
            ],

            'email' => [
                'info'  => 'Email',
                'title' => 'Email',

                'email-settings' => [
                    'admin-email'           => 'Admin Email',
                    'admin-email-tip'       => 'The email address of the admin for this channel to receive emails',
                    'admin-name'            => 'Admin Name',
                    'admin-name-tip'        => 'This name will be displayed in all admin emails',
                    'admin-page-limit'      => 'Default Items Per Page (Admin)',
                    'contact-email'         => 'Contact Email',
                    'contact-email-tip'     => 'The email address will be shown at the bottom of your emails',
                    'contact-name'          => 'Contact Name',
                    'contact-name-tip'      => 'This name will be shown at the bottom of your emails',
                    'email-sender-name'     => 'Email Sender Name',
                    'email-sender-name-tip' => 'This name will be displayed in the customers inbox',
                    'info'                  => 'Set email sender name, shop email address, admin name and admin email address.',
                    'shop-email-from'       => 'Shop Email Address',
                    'shop-email-from-tip'   => 'The email address of this channel to send emails to your customers',
                    'title'                 => 'Email Settings',
                ],

                'notifications' => [
                    'cancel-order'                                     => 'Send a notification to customer after canceling an order',
                    'cancel-order-mail-to-admin'                       => 'Send a notification e-mail to admin after canceling an order',
                    'customer'                                         => 'Send the customer account credentials after registration',
                    'customer-registration-confirmation-mail-to-admin' => 'Send a confirmation e-mail to admin after customer registration',
                    'info'                                             => 'To configure, receive emails for account verification, order confirmations, updates on invoices, refunds, shipments, and order cancellations.',
                    'new-inventory-source'                             => 'Send a notification e-mail to the inventory source after creating a shipment',
                    'new-invoice'                                      => 'Send a notification e-mail to the customer after creating a new invoice',
                    'new-invoice-mail-to-admin'                        => 'Send a notification e-mail to the admin after creating a new invoice',
                    'new-order'                                        => 'Send a confirmation e-mail to the customer after placing a new order',
                    'new-order-mail-to-admin'                          => 'Send a confirmation e-mail to the admin after placing a new order',
                    'new-refund'                                       => 'Send a notification e-mail to the customer after creating a refund',
                    'new-refund-mail-to-admin'                         => 'Send a notification e-mail to the admin after creating a new refund',
                    'new-shipment'                                     => 'Send a notification e-mail to the customer after creating a shipment',
                    'new-shipment-mail-to-admin'                       => 'Send a notification e-mail to the admin after creating a new shipment',
                    'registration'                                     => 'Send a confirmation e-mail after customer registration',
                    'title'                                            => 'Notifications',
                    'verification'                                     => 'Send a verification e-mail after customer registration',
                ],
            ],

            'sales' => [
                'info'  => 'Sales',
                'title' => 'Sales',

                'shipping-setting' => [
                    'info'  => 'Configure shipping settings including Country, State, City, Street Address, Zip Code, Store Name, VAT Number, Contact Number, and Bank Details.',
                    'title' => 'Shipping Settings',

                    'origin' => [
                        'bank-details'   => 'Bank Details',
                        'city'           => 'City',
                        'contact-number' => 'Contact Number',
                        'country'        => 'Country',
                        'state'          => 'State',
                        'store-name'     => 'Store Name',
                        'street-address' => 'Street Address',
                        'title'          => 'Origin',
                        'title-info'     => 'Shipping origin refers to the location where goods or products originate before being transported to their destination.',
                        'vat-number'     => 'Vat Number',
                        'zip'            => 'Zip',
                    ],
                ],

                'shipping-methods' => [
                    'info'  => 'Configure shipping methods, including Free Shipping, Flat Rate, and additional options as needed.',
                    'title' => 'Shipping Methods',

                    'free-shipping' => [
                        'description' => 'Description',
                        'page-title'  => 'Free Shipping',
                        'status'      => 'Status',
                        'title'       => 'Title',
                        'title-info'  => '"Free shipping" refers to a shipping method where the cost of shipping is waived, and the seller covers the shipping expenses for delivering goods to the buyer.',
                    ],

                    'flat-rate-shipping' => [
                        'description' => 'Description',
                        'page-title'  => 'Flat Rate Shipping',
                        'rate'        => 'Rate',
                        'status'      => 'Status',
                        'title'       => 'Title',
                        'title-info'  => 'Flat rate shipping is a shipping method where a fixed fee is charged for shipping, regardless of the weight, size, or distance of the package. This simplifies shipping costs and can be advantageous for both buyers and sellers.',
                        'type'        => [
                            'per-order' => 'Per Order',
                            'per-unit'  => 'Per Unit',
                            'title'     => 'Type',
                        ],
                    ],
                ],

                'payment-methods' => [
                    'accepted-currencies'            => 'Accepted currencies',
                    'accepted-currencies-info'       => 'Add currency code comma separated e.g. USD,INR,...',
                    'business-account'               => 'Business Account',
                    'cash-on-delivery'               => 'Cash On Delivery',
                    'cash-on-delivery-info'          => 'Payment method where customers pay in cash upon receiving goods or services at their doorstep.',
                    'client-id'                      => 'Client ID',
                    'client-id-info'                 => 'Use "sb" for testing.',
                    'client-secret'                  => 'Client Secret',
                    'client-secret-info'             => 'Add your secret key here',
                    'description'                    => 'Description',
                    'generate-invoice'               => 'Automatically generate the invoice after placing an order',
                    'generate-invoice-applicable'    => 'Applicable if automatic generate invoice is enabled',
                    'info'                           => 'Set payment methods information',
                    'instructions'                   => 'Instructions',
                    'logo'                           => 'Logo',
                    'logo-information'               => 'Image resolution should be like 55px X 45px',
                    'mailing-address'                => 'Send Check to',
                    'money-transfer'                 => 'Money Transfer',
                    'money-transfer-info'            => 'Transfer of funds from one person or account to another, often electronically, for various purposes such as transactions or remittances.',
                    'page-title'                     => 'Payment Methods',
                    'paid'                           => 'Paid',
                    'paypal-smart-button'            => 'PayPal',
                    'paypal-smart-button-info'       => 'PayPal Smart Button: Simplifies online payments with customizable buttons for secure, multi-method transactions on websites and apps.',
                    'paypal-standard'                => 'PayPal Standard',
                    'paypal-standard-info'           => 'PayPal Standard is a basic PayPal payment option for online businesses, enabling customers to pay using their PayPal accounts or credit/debit cards.',
                    'pending'                        => 'Pending',
                    'pending-payment'                => 'Pending Payment',
                    'processing'                     => 'Processing',
                    'sandbox'                        => 'Sandbox',
                    'set-invoice-status'             => 'Set the invoice status after creating the invoice to',
                    'set-order-status'               => 'Set the order status after creating the invoice to',
                    'sort-order'                     => 'Sort Order',
                    'status'                         => 'Status',
                    'title'                          => 'Title',
                ],

                'order-settings' => [
                    'info'               => 'Set order numbers, minimum orders and back orders.',
                    'title'              => 'Order Settings',

                    'order-number' => [
                        'generator'   => 'Order Number Generator',
                        'info'        => 'Unique identifier assigned to a specific customer order, aiding tracking, communication, and reference throughout the purchasing process.',
                        'length'      => 'Order Number Length',
                        'prefix'      => 'Order Number Prefix',
                        'suffix'      => 'Order Number Suffix',
                        'title'       => 'Order Number Settings',
                    ],

                    'minimum-order' => [
                        'description'             => 'Description',
                        'enable'                  => 'Enable',
                        'include-discount-amount' => 'Include Discount Amount',
                        'include-tax-amount'      => 'Include Tax to Amount',
                        'info'                    => 'Configured criteria specifying the lowest required quantity or value for an order to be processed or qualify for benefits.',
                        'minimum-order-amount'    => 'Minimum Order Amount',
                        'title'                   => 'Minimum Order Settings',
                    ],

                    'reorder' => [
                        'admin-reorder'      => 'Admin Reorder',
                        'admin-reorder-info' => 'Enable or disable the reordering feature for admin users.',
                        'info'               => 'Enable or disable the reordering feature for admin users.',
                        'shop-reorder'       => 'Shop Reorder',
                        'shop-reorder-info'  => 'Enable or disable the reordering feature for shop users.',
                        'title'              => 'Allow Reorder',
                    ],

                    'stock-options' => [
                        'allow-back-orders' => 'Allow Back orders',
                        'info'              => 'Stock options are investment contracts that grant the right to buy or sell company shares at a predetermined price, influencing potential profits.',
                        'title'             => 'Stock Options',
                    ],
                ],

                'invoice-settings' => [
                    'info'  => 'Set invoice number, payment terms, invoice slip design and invoice reminders.',
                    'title' => 'Invoice Settings',

                    'invoice-number' => [
                        'generator'  => 'Invoice Number Generator',
                        'info'       => 'Configuration of rules or parameters for generating and assigning unique identification numbers to invoices for organizational and tracking purposes.',
                        'length'     => 'Invoice Number Length',
                        'prefix'     => 'Invoice Number Prefix',
                        'suffix'     => 'Invoice Number Suffix',
                        'title'      => 'Invoice Number Settings',
                    ],

                    'payment-terms' => [
                        'due-duration'      => 'Due Duration',
                        'due-duration-day'  => ':due-duration Day',
                        'due-duration-days' => ':due-duration Days',
                        'info'              => 'Agreed-upon conditions dictating when and how payment for goods or services should be made by the buyer to the seller.',
                        'title'             => 'Payment Terms',
                    ],

                    'pdf-print-outs' => [
                        'footer-text'      => 'Footer text',
                        'footer-text-info' => 'Enter the text that will appear in the footer of the PDF.',
                        'info'             => 'Configure PDF Print Outs to display Invoice ID, Order ID in the header, and include the invoice logo.',
                        'invoice-id-info'  => 'Configure display of Invoice ID in Invoice Header.',
                        'invoice-id-title' => 'Display Invoice ID in Header',
                        'logo'             => 'Logo',
                        'logo-info'        => 'Image resolution should be like 131px X 30px.',
                        'order-id-info'    => 'Configure display of Order ID in Invoice Header.',
                        'order-id-title'   => 'Display Order ID in Header',
                        'title'            => 'PDF Print Outs',
                    ],

                    'invoice-reminders' => [
                        'info'                       => 'Automated notifications or communications sent to customers to remind them of upcoming or overdue payments for invoices.',
                        'interval-between-reminders' => 'Interval between reminders',
                        'maximum-limit-of-reminders' => 'Maximum limit of reminders',
                        'title'                      => 'Invoice Reminders',
                    ],
                ],

                'taxes' => [
                    'title'      => 'Taxes',
                    'title-info' => 'Taxes are mandatory fees imposed by governments on goods, services, or transactions, collected by sellers and remitted to the authorities.',

                    'categories' => [
                        'title'      => 'Tax Categories',
                        'title-info' => 'Tax categories are classifications for different types of taxes, such as sales tax, value-added tax, or excise tax, used to categorize and apply tax rates to products or services.',
                        'product'    => 'Product Default Tax Category',
                        'shipping'   => 'Shipping Tax Category',
                        'none'       => 'None',
                    ],

                    'calculation' => [
                        'title'            => 'Calculation Settings',
                        'title-info'       => 'Details about the cost of goods or services, including base price, discounts, taxes, and additional charges.information',
                        'based-on'         => 'Calculation Based On',
                        'shipping-address' => 'Shipping Address',
                        'billing-address'  => 'Billing Address',
                        'shipping-origin'  => 'Shipping Origin',
                        'product-prices'   => 'Product Prices',
                        'shipping-prices'  => 'Shipping Prices',
                        'excluding-tax'    => 'Excluding Tax',
                        'including-tax'    => 'Including Tax',
                    ],

                    'default-destination-calculation' => [
                        'default-country'   => 'Default Country',
                        'default-post-code' => 'Default Post Code',
                        'default-state'     => 'Default State',
                        'title'             => 'Default Destination Calculation',
                        'title-info'        => 'Automated determination of a standard or initial destination based on predefined factors or settings.',
                    ],

                    'shopping-cart' => [
                        'title'                   => 'Shopping Cart Display Settings',
                        'title-info'              => 'Set the display of taxes in the shopping cart',
                        'display-prices'          => 'Display Prices',
                        'display-subtotal'        => 'Display Subtotal',
                        'display-shipping-amount' => 'Display Shipping Amount',
                        'excluding-tax'           => 'Excluding Tax',
                        'including-tax'           => 'Including Tax',
                        'both'                    => 'Excluding and Including Both',
                    ],

                    'sales' => [
                        'title'                   => 'Orders, Invoices, Refunds Display Settings',
                        'title-info'              => 'Set the display of taxes in the orders, invoices, and refunds',
                        'display-prices'          => 'Display Prices',
                        'display-subtotal'        => 'Display Subtotal',
                        'display-shipping-amount' => 'Display Shipping Amount',
                        'excluding-tax'           => 'Excluding Tax',
                        'including-tax'           => 'Including Tax',
                        'both'                    => 'Excluding and Including Both',
                    ],
                ],

                'checkout' => [
                    'title' => 'Checkout',
                    'info'  => 'Set guest checkout, Enable or Disable Mini Cart, cart Summary.',

                    'shopping-cart' => [
                        'cart-page'              => 'Cart Page',
                        'cart-page-info'         => 'Control Cart Page visibility to enhance user shopping experience.',
                        'cross-sell'             => 'Cross sell Products',
                        'cross-sell-info'        => 'Enable cross-sell products to boost additional sales opportunities.',
                        'estimate-shipping'      => 'Estimated Shipping',
                        'estimate-shipping-info' => 'Enable estimated shipping to provide upfront shipping costs.',
                        'guest-checkout'         => 'Allow guest checkout',
                        'guest-checkout-info'    => 'Enable guest checkout for a faster, hassle-free purchase process.',
                        'info'                   => 'Enable guest checkout, cart page, cross-sell products, and estimated shipping to enhance user convenience and streamline the shopping process for increased sales.',
                        'title'                  => 'Shopping Cart',
                    ],

                    'my-cart' => [
                        'display-item-quantities' => 'Display item quantities',
                        'display-number-in-cart'  => 'Display number of items in cart',
                        'info'                    => 'Enable settings for My Cart to show a summary of item quantities and display the total number of items in the cart for easy tracking.',
                        'summary'                 => 'Summary',
                        'title'                   => 'My Cart',
                    ],

                    'mini-cart' => [
                        'display-mini-cart'    => 'Display Mini Cart',
                        'info'                 => 'Enable Mini Cart settings to display the mini cart and show Mini Cart Offer Information for quick access to cart details and promotions.',
                        'mini-cart-offer-info' => 'Mini Cart Offer Information',
                        'title'                => 'Mini Cart',
                    ],
                ],
            ],
        ],
    ],

    'components' => [
        'layouts' => [
            'header' => [
                'account-title' => 'Account',
                'app-version'   => 'Version : :version',
                'logout'        => 'Logout',
                'my-account'    => 'My Account',
                'notifications' => 'Notifications',
                'visit-shop'    => 'Visit Shop',

                'mega-search' => [
                    'categories'                      => 'Categories',
                    'customers'                       => 'Customers',
                    'explore-all-categories'          => 'Explore all categories',
                    'explore-all-customers'           => 'Explore all customers',
                    'explore-all-matching-categories' => 'Explore all categories matching “:query” (:count)',
                    'explore-all-matching-customers'  => 'Explore all customers matching “:query” (:count)',
                    'explore-all-matching-orders'     => 'Explore all Orders matching “:query” (:count)',
                    'explore-all-matching-products'   => 'Explore all products matching “:query” (:count)',
                    'explore-all-orders'              => 'Explore all Orders',
                    'explore-all-products'            => 'Explore all products',
                    'orders'                          => 'Orders',
                    'products'                        => 'Products',
                    'sku'                             => 'SKU: :sku',
                    'title'                           => 'Mega Search',
                ],
            ],

            'sidebar' => [
                'attribute-families'       => 'Attribute Families',
                'attributes'               => 'Attributes',
                'booking-product'          => 'Bookings',
                'campaigns'                => 'Campaigns',
                'catalog'                  => 'Catalog',
                'categories'               => 'Categories',
                'channels'                 => 'Channels',
                'cms'                      => 'CMS',
                'collapse'                 => 'Collapse',
                'communications'           => 'Communications',
                'configure'                => 'Configure',
                'currencies'               => 'Currencies',
                'customers'                => 'Customers',
                'dashboard'                => 'Dashboard',
                'enquiries'                => 'Enquiries',
                'transactions'              => 'Transactions',
                'subscriptions'             => 'Subscriptions',
                'data-transfer'            => 'Data Transfer',
                'discount'                 => 'Discount',
                'email-templates'          => 'Email Templates',
                'events'                   => 'Events',
                'exchange-rates'           => 'Exchange Rates',
                'gdpr-data-requests'       => 'GDPR Data Requests',
                'groups'                   => 'Groups',
                'imports'                  => 'Imports',
                'inventory-sources'        => 'Inventory Sources',
                'invoices'                 => 'Invoices',
                'locales'                  => 'Locales',
                'marketing'                => 'Marketing',
                'mode'                     => 'Dark Mode',
                'newsletter-subscriptions' => 'Newsletter Subscriptions',
                'orders'                   => 'Orders',
                'products'                 => 'Products',
                'promotions'               => 'Promotions',
                'refunds'                  => 'Refunds',
                'reporting'                => 'Reporting',
                'reviews'                  => 'Reviews',
                'roles'                    => 'Roles',
                'sales'                    => 'Sales',
                'search-seo'               => 'Search & SEO',
                'search-synonyms'          => 'Search Synonyms',
                'search-terms'             => 'Search Terms',
                'settings'                 => 'Settings',
                'shipments'                => 'Shipments',
                'sitemaps'                 => 'Sitemaps',
                'tax-categories'           => 'Tax Categories',
                'tax-rates'                => 'Tax Rates',
                'taxes'                    => 'Taxes',
                'themes'                   => 'Themes',
                'transactions'             => 'Transactions',
                'url-rewrites'             => 'URL Rewrites',
                'users'                    => 'Users',
                'vendors'                  => 'Vendors',
                'vendor-plans'             => 'Your Plans',
            ],

            'powered-by' => [
                'description' => 'Powered by :bagisto,'
            ],
        ],

        'datagrid' => [
            'index' => [
                'no-records-selected'              => 'No records have been selected.',
                'must-select-a-mass-action-option' => 'You must select a mass action\'s option.',
                'must-select-a-mass-action'        => 'You must select a mass action.',
            ],

            'toolbar' => [
                'length-of' => ':length of',
                'of'        => 'of',
                'per-page'  => 'Per Page',
                'results'   => ':total Results',
                'selected'  => ':total Selected',

                'mass-actions' => [
                    'submit'        => 'Submit',
                    'select-option' => 'Select Option',
                    'select-action' => 'Select Action',
                ],

                'filter' => [
                    'apply-filters-btn' => 'Apply Filters',
                    'back-btn'          => 'Back',
                    'create-new-filter' => 'Create New Filter',
                    'custom-filters'    => 'Custom Filters',
                    'delete-error'      => 'Something went wrong while deleting the filter, please try again.',
                    'delete-success'    => 'Filter has been deleted successfully.',
                    'empty-description' => 'There is no selected filters available to save. Please select filters to save.',
                    'empty-title'       => 'Add Filters to Save',
                    'name'              => 'Name',
                    'quick-filters'     => 'Quick Filters',
                    'save-btn'          => 'Save',
                    'save-filter'       => 'Save Filter',
                    'saved-success'     => 'Filter has been saved successfully.',
                    'selected-filters'  => 'Selected Filters',
                    'title'             => 'Filter',
                    'update'            => 'Update',
                    'update-filter'     => 'Update Filter',
                    'updated-success'   => 'Filter has been updated successfully.',
                ],

                'search' => [
                    'title' => 'Search',
                ],
            ],

            'filters' => [
                'select' => 'Select',
                'title'  => 'Filters',

                'dropdown' => [
                    'searchable' => [
                        'atleast-two-chars' => 'Type atleast 2 characters...',
                        'no-results'        => 'No result found...',
                    ],
                ],

                'custom-filters' => [
                    'clear-all' => 'Clear All',
                    'title'     => 'Custom Filters',
                ],

                'boolean-options' => [
                    'false' => 'False',
                    'true'  => 'True',
                ],

                'date-options' => [
                    'last-month'        => 'Last Month',
                    'last-six-months'   => 'Last 6 Months',
                    'last-three-months' => 'Last 3 Months',
                    'this-month'        => 'This Month',
                    'this-week'         => 'This Week',
                    'this-year'         => 'This Year',
                    'today'             => 'Today',
                    'yesterday'         => 'Yesterday',
                ],
            ],

            'table' => [
                'actions'              => 'Actions',
                'no-records-available' => 'No Records Available.',
            ],
        ],

        'modal' => [
            'confirm' => [
                'agree-btn'    => 'Agree',
                'disagree-btn' => 'Disagree',
                'message'      => 'Are you sure you want to perform this action?',
                'title'        => 'Are you sure?',
            ],
        ],

        'products' => [
            'search' => [
                'add-btn'       => 'Add Selected Product',
                'empty-info'    => 'No products available for search term.',
                'empty-title'   => 'No products found',
                'product-image' => 'Product Image',
                'qty'           => ':qty Available',
                'sku'           => 'SKU - :sku',
                'title'         => 'Select Products',
            ],
        ],

        'media' => [
            'images' => [
                'add-image-btn'     => 'Add Image',
                'ai-add-image-btn'  => 'Magic AI',
                'ai-btn-info'       => 'Generate Image',
                'allowed-types'     => 'png, jpeg, jpg',
                'not-allowed-error' => 'Only images files (.jpeg, .jpg, .png, ..) are allowed.',

                'ai-generation' => [
                    '1024x1024'        => '1024x1024',
                    '1024x1792'        => '1024x1792',
                    '1792x1024'        => '1792x1024',
                    'apply'            => 'Apply',
                    'dall-e-2'         => 'Dall.E 2',
                    'dall-e-3'         => 'Dall.E 3',
                    'generate'         => 'Generate',
                    'generating'       => 'Generating...',
                    'hd'               => 'HD',
                    'model'            => 'Model',
                    'number-of-images' => 'Number of Images',
                    'prompt'           => 'Prompt',
                    'quality'          => 'Quality',
                    'regenerate'       => 'Regenerate',
                    'regenerating'     => 'Regenerating...',
                    'size'             => 'Size',
                    'standard'         => 'Standard',
                    'title'            => 'AI Image Generation',
                ],

                'placeholders' => [
                    'front'     => 'Front',
                    'next'      => 'Next',
                    'size'      => 'Size',
                    'use-cases' => 'Use Cases',
                    'zoom'      => 'Zoom',
                ],
            ],

            'videos' => [
                'add-video-btn'     => 'Add Video',
                'allowed-types'     => 'mp4, webm, mkv',
                'not-allowed-error' => 'Only videos files (.mp4, .mov, .ogg ..) are allowed.',
            ],
        ],

        'tinymce' => [
            'ai-btn-tile' => 'Magic AI',

            'ai-generation' => [
                'apply'                    => 'Apply',
                'deepseek-r1-8b'           => 'DeepSeek R1 (8b)',
                'enabled'                  => 'Enabled',
                'gemini-2-0-flash'         => 'Gemini 2.0 Flash',
                'generate'                 => 'Generate',
                'generated-content'        => 'Generated Content',
                'generated-content-info'   => 'AI-generated content may be misleading. Review the generated content before applying.',
                'generating'               => 'Generating...',
                'gpt-4-turbo'              => 'OpenAI gpt-4 Turbo',
                'gpt-4o'                   => 'OpenAI gpt-4o',
                'gpt-4o-mini'              => 'OpenAI gpt-4o Mini',
                'llama-groq'               => 'Llama 3.3 (Groq)',
                'llama3-1-8b'              => '(Ollama) Llama 3.1 (8B)',
                'llama3-2-1b'              => '(Ollama) Llama 3.2 (1B)',
                'llama3-2-3b'              => '(Ollama) Llama 3.2 (3B)',
                'llama3-8b'                => '(Ollama) Llama 3 (8B)',
                'llava-7b'                 => 'Llava (7b)',
                'mistral-7b'               => 'Mistral (7b)',
                'model'                    => 'Model',
                'orca-mini'                => 'Orca Mini',
                'phi3-5'                   => 'Phi 3.5',
                'prompt'                   => 'Prompt',
                'qwen2-5-0-5b'             => 'Qwen 2.5 (0.5b)',
                'qwen2-5-1-5b'             => 'Qwen 2.5 (1.5b)',
                'qwen2-5-14b'              => 'Qwen 2.5 (14b)',
                'qwen2-5-3b'               => 'Qwen 2.5 (3b)',
                'qwen2-5-7b'               => 'Qwen 2.5 (7b)',
                'starling-lm-7b'           => 'Starling-lm (7b)',
                'title'                    => 'AI Assistance',
                'vicuna-13b'               => 'Vicuna (13b)',
                'vicuna-7b'                => 'Vicuna (7b)',
            ],
        ],
    ],

    'acl' => [
        'addresses'                => 'Addresses',
        'attribute-families'       => 'Attribute Families',
        'attributes'               => 'Attributes',
        'campaigns'                => 'Campaigns',
        'cancel'                   => 'Cancel',
        'cart-rules'               => 'Cart Rules',
        'catalog'                  => 'Catalog',
        'catalog-rules'            => 'Catalog Rules',
        'categories'               => 'Categories',
        'channels'                 => 'Channels',
        'cms'                      => 'CMS',
        'communications'           => 'Communications',
        'configure'                => 'Configure',
        'copy'                     => 'Copy',
        'create'                   => 'Create',
        'currencies'               => 'Currencies',
        'customers'                => 'Customers',
        'dashboard'                => 'Dashboard',
        'data-transfer'            => 'Data Transfer',
        'delete'                   => 'Delete',
        'edit'                     => 'Edit',
        'email-templates'          => 'Email Templates',
        'events'                   => 'Events',
        'exchange-rates'           => 'Exchange Rates',
        'gdpr'                     => 'GDPR',
        'groups'                   => 'Groups',
        'import'                   => 'Import',
        'imports'                  => 'Imports',
        'inventory-sources'        => 'Inventory Sources',
        'invoices'                 => 'Invoices',
        'locales'                  => 'Locales',
        'marketing'                => 'Marketing',
        'newsletter-subscriptions' => 'Newsletter Subscriptions',
        'note'                     => 'Note',
        'orders'                   => 'Orders',
        'products'                 => 'Products',
        'promotions'               => 'Promotions',
        'refunds'                  => 'Refunds',
        'reporting'                => 'Reporting',
        'reviews'                  => 'Reviews',
        'roles'                    => 'Roles',
        'sales'                    => 'Sales',
        'search-seo'               => 'Search & SEO',
        'search-synonyms'          => 'Search Synonyms',
        'search-terms'             => 'Search Terms',
        'settings'                 => 'Settings',
        'shipments'                => 'Shipments',
        'sitemaps'                 => 'Sitemaps',
        'subscribers'              => 'Newsletter Subscribers',
        'tax-categories'           => 'Tax Categories',
        'tax-rates'                => 'Tax Rates',
        'taxes'                    => 'Taxes',
        'themes'                   => 'Themes',
        'transactions'             => 'Transactions',
        'url-rewrites'             => 'URL Rewrites',
        'users'                    => 'Users',
        'view'                     => 'View',
    ],

    'errors' => [
        'dashboard' => 'Dashboard',
        'go-back'   => 'Go Back',
        'support'   => 'If the problem persists, reach out to us at <a href=":link" class=":class">:email</a> for assistance.',

        '404' => [
            'description' => 'Oops! The page you\'re looking for is on vacation. It seems we couldn\'t find what you were searching for.',
            'title'       => '404 Page Not Found',
        ],

        '401' => [
            'description' => 'Oops! Looks like you\'re not allowed to access this page. It seems you\'re missing the necessary credentials.',
            'title'       => '401 Unauthorized',
        ],

        '403' => [
            'description' => 'Oops! This page is off-limits. It appears you don\'t have the required permissions to view this content.',
            'title'       => '403 Forbidden',
        ],

        '500' => [
            'description' => 'Oops! Something went wrong. It seems we\'re having trouble loading the page you\'re looking for.',
            'title'       => '500 Internal Server Error',
        ],

        '503' => [
            'description' => 'Oops! Looks like we\'re temporarily down for maintenance. Please check back in a bit.',
            'title'       => '503 Service Unavailable',
        ],
    ],

    'export' => [
        'csv'        => 'CSV',
        'download'   => 'Download',
        'export'     => 'Export',
        'no-records' => 'Nothing to export',
        'xls'        => 'XLS',
        'xlsx'       => 'XLSX',
    ],

    'validations' => [
        'slug-being-used' => 'This slug is getting used in either categories or products.',
        'slug-reserved'   => 'This slug is reserved.',
    ],

    'footer' => [
        'copy-right' => 'Powered by <a href="https://bagisto.com/" target="_blank">Bagisto</a>, A Community Project by <a href="https://webkul.com/" target="_blank">Webkul</a>',
    ],

    'emails' => [
        'dear'   => 'Dear :admin_name',
        'thanks' => 'If you need any kind of help please contact us at <a href=":link" style=":style">:email</a>.<br/>Thanks!',

        'admin' => [
            'forgot-password' => [
                'description'    => 'You are receiving this email because we received a password reset request for your account.',
                'greeting'       => 'Forgot Password!',
                'reset-password' => 'Reset Password',
                'subject'        => 'Reset Password Email',
            ],
        ],

        'customers' => [
            'registration' => [
                'description' => 'A new customer account has been successfully created. They can now log in using their email address and password credentials. Once logged in, they will have access to various services, including the ability to review past orders, manage wishlists, and update their account information.',
                'greeting'    => 'We extend a warm welcome to the new customer, :customer_name who has just registered with us!',
                'subject'     => 'New Customer Registration',
            ],

            'gdpr' => [
                'new-delete-request' => 'New Request For Data Delete',
                'new-update-request' => 'New Request For Data Update',

                'new-request' => [
                    'customer-name'  => 'Customer Name : ',
                    'delete-summary' => 'Summary of Delete Request',
                    'message'        => 'Message : ',
                    'request-status' => 'Request Status : ',
                    'request-type'   => 'Request Type : ',
                    'update-summary' => 'Summary of Update Request',
                ],

                'status-update' => [
                    'subject'        => 'GDPR Request Has Been Updated',
                    'summary'        => 'The GDPR Request Status Has Been Updated',
                    'request-status' => 'Request Status:',
                    'request-type'   => 'Request Type:',
                    'message'        => 'Message:',
                ],
            ],
        ],

        'orders' => [
            'created' => [
                'greeting' => 'You have a new Order :order_id placed on :created_at',
                'subject'  => 'New Order Confirmation',
                'summary'  => 'Summary of Order',
                'title'    => 'Order Confirmation!',
            ],

            'invoiced' => [
                'greeting' => 'Your invoice #:invoice_id for order :order_id created on :created_at',
                'subject'  => 'New Invoice Confirmation',
                'summary'  => 'Summary of Invoice',
                'title'    => 'Invoice Confirmation!',
            ],

            'shipped' => [
                'greeting' => 'You have shipped the order :order_id placed on :created_at',
                'subject'  => 'New Shipment Confirmation',
                'summary'  => 'Summary of Shipment',
                'title'    => 'Order Shipped!',
            ],

            'inventory-source' => [
                'greeting' => 'You have shipped the order :order_id placed on :created_at',
                'subject'  => 'New Shipment Confirmation',
                'summary'  => 'Summary of Shipment',
                'title'    => 'Order Shipped!',
            ],

            'refunded' => [
                'greeting' => 'You have refunded for order :order_id placed on :created_at',
                'subject'  => 'New Refund Confirmation',
                'summary'  => 'Summary of Refund',
                'title'    => 'Order Refunded!',
            ],

            'canceled' => [
                'greeting' => 'You have canceled the order :order_id placed on :created_at',
                'subject'  => 'New Order Canceled',
                'summary'  => 'Summary of Order',
                'title'    => 'Order Canceled!',
            ],

            'billing-address'            => 'Billing Address',
            'carrier'                    => 'Carrier',
            'contact'                    => 'Contact',
            'discount'                   => 'Discount',
            'excl-tax'                   => 'Excl. Tax: ',
            'grand-total'                => 'Grand Total',
            'name'                       => 'Name',
            'payment'                    => 'Payment',
            'price'                      => 'Price',
            'qty'                        => 'Qty',
            'shipping-address'           => 'Shipping Address',
            'shipping-handling-excl-tax' => 'Shipping Handling (Excl. Tax)',
            'shipping-handling-incl-tax' => 'Shipping Handling (Incl. Tax)',
            'shipping-handling'          => 'Shipping Handling',
            'shipping'                   => 'Shipping',
            'sku'                        => 'SKU',
            'subtotal-excl-tax'          => 'Subtotal (Excl. Tax)',
            'subtotal-incl-tax'          => 'Subtotal (Incl. Tax)',
            'subtotal'                   => 'Subtotal',
            'tax'                        => 'Tax',
            'tracking-number'            => 'Tracking Number : :tracking_number',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Vendors
    |--------------------------------------------------------------------------
    |
    | The following language lines are used for vendor management.
    |
    */
    'vendors' => [
        'index' => [
            'title' => 'Vendor Management',
            'create-btn' => 'Create Vendor',
            'datagrid' => [
                'id' => 'ID',
                'company-name' => 'Company Name',
                'company-email' => 'Company Email',
                'business-phone' => 'Business Phone',
                'business-type' => 'Business Type',
                'status' => 'Status',
                'verified' => 'Verified',
                'date' => 'Registration Date',
                'view' => 'View',
                'approve' => 'Approve',
                'reject' => 'Reject',
                'delete' => 'Delete',
                'pending' => 'Pending',
                'approved' => 'Approved',
                'rejected' => 'Rejected',
                'yes' => 'Yes',
                'no' => 'No',
            ],
        ],
        'view' => [
            'title' => 'Vendor Details',
            'back-btn' => 'Back',
            'approve-btn' => 'Approve Vendor',
            'reject-btn' => 'Reject Vendor',
            'general-info' => 'General Information',
            'business-info' => 'Business Information',
            'registration-info' => 'Registration Information',
            'status-info' => 'Status Information',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | ACL
    |--------------------------------------------------------------------------
    */
    'acl' => [
        'vendors' => 'Vendors',
        'vendor-plans' => 'Your Plans',
        'view' => 'View',
        'approve' => 'Approve',
        'reject' => 'Reject',
        'delete' => 'Delete',
    ],

    'vendor-plans' => [
        'index' => [
            'title' => 'Your Plans',
            'info-text' => 'View your subscription plans and their status',

            'datagrid' => [
                'id' => 'ID',
                'plan-name' => 'Plan Name',
                'description' => 'Description',
                'price' => 'Price',
                'paid-amount' => 'Paid Amount',
                'duration' => 'Duration',
                'status' => 'Status',
                'payment-status' => 'Payment Status',
                'subscription-status' => 'Subscription Status',
                'transaction-id' => 'Transaction ID',
                'expires-at' => 'Expires At',
                'subscribed-at' => 'Subscribed At',
                'view' => 'View Details',
            ],

            'status' => [
                'active' => 'Active',
                'inactive' => 'Inactive',
                'unknown' => 'Unknown',
            ],

            'subscription-status' => [
                'pending' => 'Pending',
                'active' => 'Active',
                'expired' => 'Expired',
                'unknown' => 'Unknown',
            ],
        ],

        'show' => [
            'title' => 'Plan Details',
            'back-btn' => 'Back to Your Plans',
            'plan-details' => 'Plan Information',
            'subscription-info' => 'Subscription Details',
            'plan-name' => 'Plan Name',
            'original-price' => 'Original Price',
            'paid-amount' => 'Paid Amount',
            'duration' => 'Duration',
            'payment-status' => 'Payment Status',
            'description' => 'Description',
            'transaction-id' => 'Transaction ID',
            'order-id' => 'Order ID',
            'subscribed-at' => 'Subscribed At',
            'expires-at' => 'Expires At',
            'subscription-status' => 'Subscription Status',
        ],
    ],
];
