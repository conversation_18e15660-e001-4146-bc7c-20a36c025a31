{"__meta": {"id": "01K0EVT8ND96BTRNPB4S1T0HZW", "datetime": "2025-07-18 19:03:11", "utime": **********.215327, "method": "GET", "uri": "/cache/small/product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp", "ip": "127.0.0.1"}, "modules": {"count": 1, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 26.08, "duration_str": "26.08s", "connection": "geniusdb"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.width'", "duration": 0.56, "duration_str": "560ms", "connection": "geniusdb"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.height'", "duration": 0.78, "duration_str": "780ms", "connection": "geniusdb"}]}]}, "messages": {"count": 1, "messages": [{"message": "[19:03:11] LOG.warning: Creation of dynamic property Intervention\\Image\\Image::$cachekey is deprecated in D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\bagisto\\image-cache\\src\\Intervention\\Image\\ImageCache.php on line 275", "message_html": null, "is_string": false, "label": "warning", "time": **********.201038, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.592516, "end": **********.229814, "duration": 0.6372981071472168, "duration_str": "637ms", "measures": [{"label": "Booting", "start": **********.592516, "relative_start": 0, "end": **********.993254, "relative_end": **********.993254, "duration": 0.***************, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.993271, "relative_start": 0.****************, "end": **********.229816, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "237ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.015307, "relative_start": 0.****************, "end": **********.021025, "relative_end": **********.021025, "duration": 0.0057179927825927734, "duration_str": "5.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.209964, "relative_start": 0.***************, "end": **********.210132, "relative_end": **********.210132, "duration": 0.****************, "duration_str": "168μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.210153, "relative_start": 0.****************, "end": **********.210174, "relative_end": **********.210174, "duration": 2.09808349609375e-05, "duration_str": "21μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "34MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02742, "accumulated_duration_str": "27.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "type": "query", "params": [], "bindings": ["127.0.0.1:8000", "http://127.0.0.1:8000", "https://127.0.0.1:8000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 171}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 248}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/SystemConfig.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\SystemConfig.php", "line": 218}], "start": **********.1412332, "duration": 0.02608, "duration_str": "26.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "geniusdb", "explain": null, "start_percent": 0, "width_percent": 95.113}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.width'", "type": "query", "params": [], "bindings": ["catalog.products.cache_small_image.width"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.172348, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 95.113, "width_percent": 2.042}, {"sql": "select * from `core_config` where `code` = 'catalog.products.cache_small_image.height'", "type": "query", "params": [], "bindings": ["catalog.products.cache_small_image.height"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.1835518, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 97.155, "width_percent": 2.845}]}, "models": {"data": {"Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/small/product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "639ms", "peak_memory": "36MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-811859278 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-811859278\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-64947221 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-64947221\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1354995783 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">http://127.0.0.1:8000/directing-the-erp-implementation</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InZyU0c1R3ltbFZ4eXVQYnB2d05EYmc9PSIsInZhbHVlIjoiaXVKNElCYlJjcWRMSENwNWlMMkkvYnhCeWdYME9FSmt5blpJMnNReHp1WGNqVlNUZ29IaFRXRmlMQWFnMHlWeWM1TGVSTTJCbHJDU3I4cEh6aG1hTWNWdEJWRm9wQXdjcW52OGR0R0xhWTdMcmc2QWpObnZRYXJ1Q3pKUytNMkkiLCJtYWMiOiIxOGNjYjZlYjJkOWU1YzY1MjBmMjNkYjc4ZDMxNzdhZGFjMjM1ZTU5MzI0N2M3ZTI5NGM4ZjBjZDJlMTdiMjlmIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IllYQ1BIYjBLcGlaSTYzMkdRYTBLNVE9PSIsInZhbHVlIjoiYnptdTltaWo0VmNtUGV3YUVqRjFpcUNpNnRpUWl3ZkJqblVTajgxRzRHVzhYZ2daemNUWUphNEVaWTRpSnhPQTFTaWZvWHFQa01Mb1dUd2xkSU1SRDVIaXhINGVPVGxQaElEdTVmaGE5V0psbGNxWnpMZDZKS2drbXFCTVV4d0UiLCJtYWMiOiJlN2M4MGI2ZTg1NmJiMzMzZjBlODc4Y2YwMjA2NmVhNzMyZThjNDBiNzRmMmJmYzljYzU4OWVmN2UxMDcxN2I3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1354995783\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-289811198 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZyU0c1R3ltbFZ4eXVQYnB2d05EYmc9PSIsInZhbHVlIjoiaXVKNElCYlJjcWRMSENwNWlMMkkvYnhCeWdYME9FSmt5blpJMnNReHp1WGNqVlNUZ29IaFRXRmlMQWFnMHlWeWM1TGVSTTJCbHJDU3I4cEh6aG1hTWNWdEJWRm9wQXdjcW52OGR0R0xhWTdMcmc2QWpObnZRYXJ1Q3pKUytNMkkiLCJtYWMiOiIxOGNjYjZlYjJkOWU1YzY1MjBmMjNkYjc4ZDMxNzdhZGFjMjM1ZTU5MzI0N2M3ZTI5NGM4ZjBjZDJlMTdiMjlmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IllYQ1BIYjBLcGlaSTYzMkdRYTBLNVE9PSIsInZhbHVlIjoiYnptdTltaWo0VmNtUGV3YUVqRjFpcUNpNnRpUWl3ZkJqblVTajgxRzRHVzhYZ2daemNUWUphNEVaWTRpSnhPQTFTaWZvWHFQa01Mb1dUd2xkSU1SRDVIaXhINGVPVGxQaElEdTVmaGE5V0psbGNxWnpMZDZKS2drbXFCTVV4d0UiLCJtYWMiOiJlN2M4MGI2ZTg1NmJiMzMzZjBlODc4Y2YwMjA2NmVhNzMyZThjNDBiNzRmMmJmYzljYzU4OWVmN2UxMDcxN2I3IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289811198\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-489852443 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3814</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">7318a8862f3d3f1baced93ea83147dcc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 13:33:11 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-489852443\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-517144040 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-517144040\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/small/product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}