{"__meta": {"id": "01K03XNX1FFVVZACZ60DVGF287", "datetime": "2025-07-14 13:04:06", "utime": **********.640077, "method": "GET", "uri": "/admin/dashboard/stats?type=total-visitors", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Visit (35)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 0.47, "duration_str": "470ms", "connection": "geniusdb"}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-05-15 00:00:00' and '2025-06-14 00:00:00'", "duration": 1.81, "duration_str": "1.81s", "connection": "geniusdb"}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-06-14 00:00:00' and '2025-07-14 13:04:06'", "duration": 0.67, "duration_str": "670ms", "connection": "geniusdb"}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-05-15 00:00:00' and '2025-06-14 00:00:00' group by CONCAT(ip, \"-\", visitor_id)", "duration": 18.73, "duration_str": "18.73s", "connection": "geniusdb"}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-06-14 00:00:00' and '2025-07-14 13:04:06' group by CONCAT(ip, \"-\", visitor_id)", "duration": 7.09, "duration_str": "7.09s", "connection": "geniusdb"}, {"sql": "select DAYOFYEAR(created_at) AS date, COUNT(*) AS total from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-06-14 00:00:00' and '2025-07-14 13:04:06' group by `date`", "duration": 1.31, "duration_str": "1.31s", "connection": "geniusdb"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\Vendor (1)"], "views": [], "queries": [{"sql": "select * from `vendors` where `id` = 8 limit 1", "duration": 0.8, "duration_str": "800ms", "connection": "geniusdb"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.6, "duration_str": "600ms", "connection": "geniusdb"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.31, "duration_str": "310ms", "connection": "geniusdb"}, {"sql": "select * from `roles` where `roles`.`id` = 2 limit 1", "duration": 0.67, "duration_str": "670ms", "connection": "geniusdb"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.046534, "end": **********.648843, "duration": 0.602308988571167, "duration_str": "602ms", "measures": [{"label": "Booting", "start": **********.046534, "relative_start": 0, "end": **********.471765, "relative_end": **********.471765, "duration": 0.****************, "duration_str": "425ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.471781, "relative_start": 0.****************, "end": **********.648846, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "177ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.492063, "relative_start": 0.****************, "end": **********.496544, "relative_end": **********.496544, "duration": 0.004480838775634766, "duration_str": "4.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.637862, "relative_start": 0.****************, "end": **********.638209, "relative_end": **********.638209, "duration": 0.000347137451171875, "duration_str": "347μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03417, "accumulated_duration_str": "34.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.562836, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 0, "width_percent": 3.687}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.569752, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 3.687, "width_percent": 1.756}, {"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "type": "query", "params": [], "bindings": ["127.0.0.1:8000", "http://127.0.0.1:8000", "https://127.0.0.1:8000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.575416, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "geniusdb", "explain": null, "start_percent": 5.443, "width_percent": 1.375}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.5769062, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 6.819, "width_percent": 1.317}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.578146, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 8.136, "width_percent": 0.907}, {"sql": "select * from `vendors` where `id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 20}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.584645, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "geniusdb", "explain": null, "start_percent": 9.043, "width_percent": 2.341}, {"sql": "select * from `roles` where `roles`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 84}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 61}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.588682, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "admin:84", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 84}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=84", "ajax": false, "filename": "Bouncer.php", "line": "84"}, "connection": "geniusdb", "explain": null, "start_percent": 11.384, "width_percent": 1.961}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-05-15 00:00:00' and '2025-06-14 00:00:00'", "type": "query", "params": [], "bindings": [1, "2025-05-15 00:00:00", "2025-06-14 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 60}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 29}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.590466, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:60", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FVisitor.php&line=60", "ajax": false, "filename": "Visitor.php", "line": "60"}, "connection": "geniusdb", "explain": null, "start_percent": 13.345, "width_percent": 5.297}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-06-14 00:00:00' and '2025-07-14 13:04:06'", "type": "query", "params": [], "bindings": [1, "2025-06-14 00:00:00", "2025-07-14 13:04:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 60}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 30}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.593457, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Visitor.php:60", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FVisitor.php&line=60", "ajax": false, "filename": "Visitor.php", "line": "60"}, "connection": "geniusdb", "explain": null, "start_percent": 18.642, "width_percent": 1.961}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-05-15 00:00:00' and '2025-06-14 00:00:00' group by CONCAT(ip, \"-\", visitor_id)", "type": "query", "params": [], "bindings": [1, "2025-05-15 00:00:00", "2025-06-14 00:00:00"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 105}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 72}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5959861, "duration": 0.01873, "duration_str": "18.73ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:105", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FVisitor.php&line=105", "ajax": false, "filename": "Visitor.php", "line": "105"}, "connection": "geniusdb", "explain": null, "start_percent": 20.603, "width_percent": 54.814}, {"sql": "select * from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-06-14 00:00:00' and '2025-07-14 13:04:06' group by CONCAT(ip, \"-\", visitor_id)", "type": "query", "params": [], "bindings": [1, "2025-06-14 00:00:00", "2025-07-14 13:04:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 105}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 73}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 120}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.616654, "duration": 0.00709, "duration_str": "7.09ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:105", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FVisitor.php&line=105", "ajax": false, "filename": "Visitor.php", "line": "105"}, "connection": "geniusdb", "explain": null, "start_percent": 75.417, "width_percent": 20.749}, {"sql": "select DAYOFYEAR(created_at) AS date, COUNT(*) AS total from `visits` where `visitable_id` is null and `channel_id` in (1) and `created_at` between '2025-06-14 00:00:00' and '2025-07-14 13:04:06' group by `date`", "type": "query", "params": [], "bindings": [1, "2025-06-14 00:00:00", "2025-07-14 13:04:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 204}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 126}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Dashboard.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Dashboard.php", "line": 121}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/DashboardController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\DashboardController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.625743, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "Visitor.php:204", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Admin/src/Helpers/Reporting/Visitor.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Helpers\\Reporting\\Visitor.php", "line": 204}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHelpers%2FReporting%2FVisitor.php&line=204", "ajax": false, "filename": "Visitor.php", "line": "204"}, "connection": "geniusdb", "explain": null, "start_percent": 96.166, "width_percent": 3.834}]}, "models": {"data": {"Webkul\\Core\\Models\\Visit": {"value": 35, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FVisit.php&line=1", "ajax": false, "filename": "Visit.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Customer\\Models\\Vendor": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FVendor.php&line=1", "ajax": false, "filename": "Vendor.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 38, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/dashboard/stats?type=total-visitors", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats", "uri": "GET admin/dashboard/stats", "controller": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/dashboard", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FDashboardController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/DashboardController.php:48-56</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin", "duration": "604ms", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-876949879 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"14 characters\">total-visitors</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-876949879\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-369620516 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-369620516\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1963266543 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlIrQWV4OVBWcDkxSUdNOHlWOFVkSVE9PSIsInZhbHVlIjoiSUxCcDFyY0pWUzExQlVGdkZUSTB2UXZyRS9iVXYyQ09jWWJvZDVKSTRDVnZjdWNWV3dUaGp3S1I5cmsrN1Y4b1V2U2cyWUZHZWFkNGpvdXpiSStFQlVxMFRrUVpxTzIxMy9GbmdDeTRRTzg2YWNnNCtkcjRrVEJqNmVKd0t0bXciLCJtYWMiOiI4YjhhZjg1OGM3NzJmMjM1NzNjNWEzOWIyYzg4YjJmMjJmNmY2YjdjYTBmMDZiM2FkNjgwYzE0YjI2MjJkYjAzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlIrQWV4OVBWcDkxSUdNOHlWOFVkSVE9PSIsInZhbHVlIjoiSUxCcDFyY0pWUzExQlVGdkZUSTB2UXZyRS9iVXYyQ09jWWJvZDVKSTRDVnZjdWNWV3dUaGp3S1I5cmsrN1Y4b1V2U2cyWUZHZWFkNGpvdXpiSStFQlVxMFRrUVpxTzIxMy9GbmdDeTRRTzg2YWNnNCtkcjRrVEJqNmVKd0t0bXciLCJtYWMiOiI4YjhhZjg1OGM3NzJmMjM1NzNjNWEzOWIyYzg4YjJmMjJmNmY2YjdjYTBmMDZiM2FkNjgwYzE0YjI2MjJkYjAzIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IjR0NkQyUGxnK0ZPNk9meVUrcXh5aHc9PSIsInZhbHVlIjoiRzczeDJuKytIY1EvTEVmWFNqOUxBbmpld2J2TCswVjkzTlhjQ09ZNEZpajl3YnpJTVhmSzBTNmFOV2NCMHVhNVNqb1c3ZmErand6ZjlGbm1PT0IxeW5Rd3RGeGExNkhtYW80WjdKbGRlMGs1Z3ViSHRpb2RNSFZ1K0VmQUlEL2siLCJtYWMiOiI5MTE2ZjFiOWY3NGNkNjkyMzgwY2NjMWVlYTY0ZTRhMDkzMDRkMGZiYzRiMTA3YWZlZmQ2ODdlYzlhZmMzNTA4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963266543\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-773548931 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">22PabLRJWo5JDWyXjvl1f7ysPbnJSnAbV3HPNor9</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VqIzI3WJAIMWxZ5pyEeTEvs12YJLWNjJDhxEEDTf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-773548931\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1096986738 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 07:34:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096986738\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-257980031 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">22PabLRJWo5JDWyXjvl1f7ysPbnJSnAbV3HPNor9</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_vendor_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>8</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-257980031\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/admin/dashboard/stats?type=total-visitors", "action_name": "admin.dashboard.stats", "controller_action": "Webkul\\Admin\\Http\\Controllers\\DashboardController@stats"}, "badge": null}}