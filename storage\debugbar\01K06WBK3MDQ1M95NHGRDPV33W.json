{"__meta": {"id": "01K06WBK3MDQ1M95NHGRDPV33W", "datetime": "2025-07-15 16:38:43", "utime": **********.509841, "method": "GET", "uri": "/api/products?sort=name-desc&limit=12", "ip": "127.0.0.1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (25)", "Webkul\\Attribute\\Models\\AttributeFamily (1)"], "views": [], "queries": [{"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "duration": 0.99, "duration_str": "990ms", "connection": "geniusdb"}, {"sql": "select * from `attributes` where `code` = 'name'", "duration": 0.94, "duration_str": "940ms", "connection": "geniusdb"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.66, "duration_str": "660ms", "connection": "geniusdb"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.06, "duration_str": "1.06s", "connection": "geniusdb"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 7.03, "duration_str": "7.03s", "connection": "geniusdb"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.11, "duration_str": "1.11s", "connection": "geniusdb"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.9, "duration_str": "900ms", "connection": "geniusdb"}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "duration": 0.73, "duration_str": "730ms", "connection": "geniusdb"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (1)"], "views": [], "queries": [{"sql": "select * from `customer_groups` where `code` = 'guest'", "duration": 0.54, "duration_str": "540ms", "connection": "geniusdb"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (1)", "Webkul\\Product\\Models\\ProductAttributeValue (19)", "Webkul\\Product\\Models\\ProductPriceIndex (3)", "Webkul\\Product\\Models\\ProductInventoryIndex (1)"], "views": [], "queries": [{"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 and `product_price_indices`.`channel_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "duration": 18.84, "duration_str": "18.84s", "connection": "geniusdb"}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (5) order by `position` asc", "duration": 0.65, "duration_str": "650ms", "connection": "geniusdb"}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (5) order by `position` asc", "duration": 0.44, "duration_str": "440ms", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (5)", "duration": 1.29, "duration_str": "1.29s", "connection": "geniusdb"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (5)", "duration": 0.76, "duration_str": "760ms", "connection": "geniusdb"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (5)", "duration": 0.47, "duration_str": "470ms", "connection": "geniusdb"}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (5)", "duration": 0.83, "duration_str": "830ms", "connection": "geniusdb"}, {"sql": "select * from `products` where `products`.`parent_id` in (5)", "duration": 0.56, "duration_str": "560ms", "connection": "geniusdb"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.79, "duration_str": "790ms", "connection": "geniusdb"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.33, "duration_str": "330ms", "connection": "geniusdb"}]}]}, "messages": {"count": 2, "messages": [{"message": "[16:38:43] LOG.warning: Creation of dynamic property Webkul\\Shop\\Http\\Resources\\ProductResource::$reviewHelper is deprecated in D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Resources\\ProductResource.php on line 18", "message_html": null, "is_string": false, "label": "warning", "time": **********.391464, "xdebug_link": null, "collector": "log"}, {"message": "[16:38:43] LOG.warning: round(): Passing null to parameter #1 ($num) of type int|float is deprecated in D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Review.php on line 28", "message_html": null, "is_string": false, "label": "warning", "time": **********.501834, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752577722.671181, "end": **********.526337, "duration": 0.8551559448242188, "duration_str": "855ms", "measures": [{"label": "Booting", "start": 1752577722.671181, "relative_start": 0, "end": **********.138425, "relative_end": **********.138425, "duration": 0.*****************, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.138445, "relative_start": 0.*****************, "end": **********.526343, "relative_end": 6.198883056640625e-06, "duration": 0.*****************, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.160814, "relative_start": 0.*****************, "end": **********.167295, "relative_end": **********.167295, "duration": 0.0064809322357177734, "duration_str": "6.48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.391871, "relative_start": 0.****************, "end": **********.507235, "relative_end": **********.507235, "duration": 0.*****************, "duration_str": "115ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::products.prices.index", "start": **********.501052, "relative_start": 0.****************, "end": **********.501052, "relative_end": **********.501052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "shop::products.prices.index", "param_count": null, "params": [], "start": **********.501022, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/products/prices/index.blade.phpshop::products.prices.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fproducts%2Fprices%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}]}, "queries": {"count": 22, "nb_statements": 22, "nb_visible_statements": 22, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.07601, "accumulated_duration_str": "76.01ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "type": "query", "params": [], "bindings": ["127.0.0.1:8000", "http://127.0.0.1:8000", "https://127.0.0.1:8000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.220461, "duration": 0.007030000000000001, "duration_str": "7.03ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "geniusdb", "explain": null, "start_percent": 0, "width_percent": 9.249}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.243523, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 9.249, "width_percent": 1.46}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.248064, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 10.709, "width_percent": 1.184}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.260756, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 11.893, "width_percent": 1.513}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.263332, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 13.406, "width_percent": 1.039}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.265436, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 14.445, "width_percent": 0.881}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.267151, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 15.327, "width_percent": 0.434}, {"sql": "select * from `customer_groups` where `code` = 'guest'", "type": "query", "params": [], "bindings": ["guest"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 746}, {"index": 19, "namespace": null, "name": "packages/Webkul/Customer/src/Repositories/CustomerRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Customer\\src\\Repositories\\CustomerRepository.php", "line": 41}], "start": **********.294497, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "geniusdb", "explain": null, "start_percent": 15.761, "width_percent": 0.71}, {"sql": "select `id`, `code`, `value_per_channel`, `value_per_locale`, `type`, `is_filterable`, `is_configurable` from `attributes` where `code` in ('sort', 'limit', 'channel_id', 'status', 'visible_individually', 'url_key')", "type": "query", "params": [], "bindings": ["sort", "limit", "channel_id", "status", "visible_individually", "url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 175}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 303}, {"index": 18, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 485}], "start": **********.2967222, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "geniusdb", "explain": null, "start_percent": 16.472, "width_percent": 1.302}, {"sql": "select * from `attributes` where `code` = 'name'", "type": "query", "params": [], "bindings": ["name"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 394}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 1033}], "start": **********.3010368, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "geniusdb", "explain": null, "start_percent": 17.774, "width_percent": 1.237}, {"sql": "select count(*) as aggregate from (select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 and `product_price_indices`.`channel_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": [1, 1, 2, "en", "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.304418, "duration": 0.03527, "duration_str": "35.27ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 19.011, "width_percent": 46.402}, {"sql": "select distinct `products`.* from `products` left join `products` as `variants` on COALESCE(variants.parent_id, variants.id) = `products`.`id` left join `product_price_indices` on `products`.`id` = `product_price_indices`.`product_id` and `product_price_indices`.`customer_group_id` = 1 and `product_price_indices`.`channel_id` = 1 left join `product_channels` on `products`.`id` = `product_channels`.`product_id` left join `product_attribute_values` as `url_key_product_attribute_values` on `products`.`id` = `url_key_product_attribute_values`.`product_id` left join `product_attribute_values` as `visible_individually_product_attribute_values` on `products`.`id` = `visible_individually_product_attribute_values`.`product_id` left join `product_attribute_values` as `status_product_attribute_values` on `products`.`id` = `status_product_attribute_values`.`product_id` left join `product_attribute_values` as `sort_product_attribute_values` on `products`.`id` = `sort_product_attribute_values`.`product_id` and `sort_product_attribute_values`.`attribute_id` = 2 and `sort_product_attribute_values`.`locale` = 'en' where `product_channels`.`channel_id` = '1' and `url_key_product_attribute_values`.`attribute_id` = 3 and `url_key_product_attribute_values`.`text_value` is not null and `visible_individually_product_attribute_values`.`attribute_id` = 7 and `visible_individually_product_attribute_values`.`boolean_value` = 1 and `status_product_attribute_values`.`attribute_id` = 8 and `status_product_attribute_values`.`boolean_value` = 1 group by `products`.`id` order by `sort_product_attribute_values`.`text_value` desc limit 12 offset 0", "type": "query", "params": [], "bindings": [1, 1, 2, "en", "1", 3, 7, 1, 8, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 20, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.3418958, "duration": 0.01884, "duration_str": "18.84ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 65.412, "width_percent": 24.786}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.363667, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 90.199, "width_percent": 0.868}, {"sql": "select * from `product_images` where `product_images`.`product_id` in (5) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.368402, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 91.067, "width_percent": 0.855}, {"sql": "select * from `product_videos` where `product_videos`.`product_id` in (5) order by `position` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.3706222, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 91.922, "width_percent": 0.579}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.372504, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 92.501, "width_percent": 1.697}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.376144, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 94.198, "width_percent": 1}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.37839, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 95.198, "width_percent": 0.618}, {"sql": "select * from `product_reviews` where `product_reviews`.`product_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.38093, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 95.816, "width_percent": 1.092}, {"sql": "select * from `products` where `products`.`parent_id` in (5)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, {"index": 22, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 434}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 222}, {"index": 25, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\ProductController.php", "line": 35}], "start": **********.383012, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:487", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 487}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=487", "ajax": false, "filename": "BaseRepository.php", "line": "487"}, "connection": "geniusdb", "explain": null, "start_percent": 96.908, "width_percent": 0.737}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 515}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 397}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/DelegatesToResource.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php", "line": 139}], "start": **********.405336, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "geniusdb", "explain": null, "start_percent": 97.645, "width_percent": 1.395}, {"sql": "select * from `currency_exchange_rates` where `target_currency` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 119}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 457}, {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 479}], "start": **********.494871, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 99.04, "width_percent": 0.96}]}, "models": {"data": {"Webkul\\Attribute\\Models\\Attribute": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductAttributeValue": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductPriceIndex": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductPriceIndex.php&line=1", "ajax": false, "filename": "ProductPriceIndex.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventoryIndex": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventoryIndex.php&line=1", "ajax": false, "filename": "ProductInventoryIndex.php", "line": "?"}}}, "count": 54, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/products?limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index", "uri": "GET api/products", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FProductController.php&line=26\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/ProductController.php:26-57</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "860ms", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-599554476 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sort</span>\" => \"<span class=sf-dump-str title=\"9 characters\">name-desc</span>\"\n  \"<span class=sf-dump-key>limit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599554476\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1897173015 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1897173015\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-693107250 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5ScVVZWnpzdXBnelB6L29FNGNacWc9PSIsInZhbHVlIjoiQ0lCZ3RTRUlyK1JXOVJWVFZIMGhzWXZDZlduUTcvNkJ4OUxObDZIZTVIWVY0YitZSnlRNlcxRllnekZ2UjlFWXNYL0JLK0hyWG5Mb3RHQ0RPZjcvLzVMRmZlcFBmcDUwcys0RDV5d2drL0FtNEpLQ0RERHdNaVNrKzVuWHArU3EiLCJtYWMiOiIzNDM3ZGQwZmYxN2UwYmM2ODNlY2MxOGM4ZmZlYjkzZmQ5ZGE0ODg0MDAwNmY4ZWI3Y2E0MzUzYTVmNzdkMjkyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik5ScVVZWnpzdXBnelB6L29FNGNacWc9PSIsInZhbHVlIjoiQ0lCZ3RTRUlyK1JXOVJWVFZIMGhzWXZDZlduUTcvNkJ4OUxObDZIZTVIWVY0YitZSnlRNlcxRllnekZ2UjlFWXNYL0JLK0hyWG5Mb3RHQ0RPZjcvLzVMRmZlcFBmcDUwcys0RDV5d2drL0FtNEpLQ0RERHdNaVNrKzVuWHArU3EiLCJtYWMiOiIzNDM3ZGQwZmYxN2UwYmM2ODNlY2MxOGM4ZmZlYjkzZmQ5ZGE0ODg0MDAwNmY4ZWI3Y2E0MzUzYTVmNzdkMjkyIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6ImloWGprNXRtUDVObzd5NzRoMnA5aXc9PSIsInZhbHVlIjoiVFIvUk9IaVlOdVBaaXhDa1QxS0E2dERZOUZPQ0dsWkV3UDF2NXk0VTVEVjNwcWVuWHhtWnFHN3hYNHhBc0lmbm9CK1pLTTlYNnhadWUxeHZsbkR5cXRVbENOZ1NuWWlvQzVGNjhGZWtHM1g3WCt0dFRqRHl0WDZ3bk9tZ1VUdWYiLCJtYWMiOiIxZDhlZmU0MjJhODZhOWFhZDEyYzMwMzAzYzM1YWM3NTg1YmVmMzM4YzJkMWM4YzBlZmI5MzU2OWNkYTk0NGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-693107250\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1239404357 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Pt1ZvkhiGucxWlHTlL2sXxuoWt6GjcgrcVvBLBIC</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Xc8QFYvNhRsHdqGWjj2wHrFt7sIsM6g1ogUwv09k</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239404357\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-61404343 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 11:08:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-61404343\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1248539776 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Pt1ZvkhiGucxWlHTlL2sXxuoWt6GjcgrcVvBLBIC</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1248539776\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/products?limit=12&sort=name-desc", "action_name": "shop.api.products.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\ProductController@index"}, "badge": null}}