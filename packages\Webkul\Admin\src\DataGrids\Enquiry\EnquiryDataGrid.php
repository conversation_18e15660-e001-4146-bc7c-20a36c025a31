<?php

namespace Webkul\Admin\DataGrids\Enquiry;

use Illuminate\Support\Facades\DB;
use Webkul\DataGrid\DataGrid;

class EnquiryDataGrid extends DataGrid
{
    /**
     * Prepare query builder.
     *
     * @return \Illuminate\Database\Query\Builder
     */
    public function prepareQueryBuilder()
    {
        $queryBuilder = DB::table('enquiries')
        ->left<PERSON>oin('customers', 'enquiries.user', '=', 'customers.id')
        ->left<PERSON>oin('products', 'enquiries.product', '=', 'products.id')
        ->leftJoin('admins', 'products.vendor', '=', 'admins.id')
        ->select(
            'enquiries.id',
            'customers.first_name as user',
            'products.sku as product',
            'products.name as product_name',
            DB::raw("CASE enquiries.status
            WHEN 0 THEN '" . trans('admin::app.enquiries.index.status.pending') . "'
                WHEN 1 THEN '" . trans('admin::app.enquiries.index.status.in-progress') . "'
                WHEN 2 THEN '" . trans('admin::app.enquiries.index.status.closed') . "'
                ELSE '" . trans('admin::app.enquiries.index.status.unknown') . "'
             END as status"),
            'enquiries.created_at',
            'enquiries.updated_at',
            'enquiries.enquired_at',
            'admins.name as vendor'
        );

        // Filter by current vendor if vendor is logged in
        if (auth()->guard('vendor')->check()) {
            $currentVendor = auth()->guard('vendor')->user();
            $queryBuilder->where('products.vendor', $currentVendor->id);
        }
        // If admin is logged in, show all enquiries (no filter)
        // This maintains admin access to all enquiries

        // Add filters for better search functionality
        $this->addFilter('id', 'enquiries.id');
        $this->addFilter('user', 'customers.first_name');
        $this->addFilter('product', 'products.sku');
        $this->addFilter('product_name', 'products.name');
        $this->addFilter('status', 'enquiries.status');
        $this->addFilter('vendor', 'admins.name');
        $this->addFilter('created_at', 'enquiries.created_at');
        $this->addFilter('enquired_at', 'enquiries.enquired_at');

        return $queryBuilder;
    }

    /**
     * Add columns.
     *
     * @return void
     */
    public function prepareColumns()
    {
        $this->addColumn([
            'index'      => 'id',
            'label'      => trans('admin::app.enquiries.index.datagrid.id'),
            'type'       => 'integer',
            'searchable' => true,
            'filterable' => true,
            'sortable'   => true,
        ]);

        $this->addColumn([
            'index'      => 'user',
            'label'      => trans('admin::app.enquiries.index.datagrid.user'),
            'type'       => 'string',
            'searchable' => true,
            'filterable' => true,
            'sortable'   => true,
        ]);

        $this->addColumn([
            'index'      => 'product_name',
            'label'      => trans('admin::app.enquiries.index.datagrid.product'),
            'type'       => 'string',
            'searchable' => true,
            'filterable' => true,
            'sortable'   => true,
            'closure'    => function ($row) {
                return '<div class="flex flex-col">
                    <p class="text-base font-semibold text-gray-800">' . ($row->product_name ?: 'N/A') . '</p>
                    <p class="text-xs text-gray-600">SKU: ' . ($row->product ?: 'N/A') . '</p>
                </div>';
            },
        ]);

        // Only show vendor column for admins, not for vendors
        if (auth()->guard('admin')->check()) {
            $this->addColumn([
                'index'      => 'vendor',
                'label'      => trans('admin::app.enquiries.index.datagrid.vendor'),
                'type'       => 'string',
                'searchable' => true,
                'filterable' => true,
                'sortable'   => true,
            ]);
        }

        $this->addColumn([
            'index'              => 'status',
            'label'              => trans('admin::app.enquiries.index.datagrid.status'),
            'type'               => 'string',
            'searchable'         => false,
            'filterable'         => true,
            'sortable'           => true,
        ]);

        $this->addColumn([
            'index'           => 'created_at',
            'label'           => trans('admin::app.enquiries.index.datagrid.created-at'),
            'type'            => 'date',
            'searchable'      => true,
            'filterable'      => true,
            'filterable_type' => 'date_range',
            'sortable'        => true,
        ]);

        $this->addColumn([
            'index'           => 'updated_at',
            'label'           => trans('admin::app.enquiries.index.datagrid.updated-at'),
            'type'            => 'date',
            'searchable'      => true,
            'filterable'      => true,
            'filterable_type' => 'date_range',
            'sortable'        => true,
        ]);


        $this->addColumn([
            'index'           => 'enquired_at',
            'label'           => trans('admin::app.enquiries.index.datagrid.enquired-at'),
            'type'            => 'date',
            'searchable'      => true,
            'filterable'      => true,
            'filterable_type' => 'date_range',
            'sortable'        => true,
        ]);
    }

    /**
     * Prepare actions.
     *
     * @return void
     */
    public function prepareActions()
    {
        if (bouncer()->hasPermission('catalog.attributes.edit')) {
            $this->addAction([
                'icon'   => 'icon-edit',
                'title'  => trans('admin::app.catalog.attributes.index.datagrid.edit'),
                'method' => 'GET',
                'url'    => function ($row) {
                    return route('admin.catalog.attributes.edit', $row->id);
                },
            ]);
        }

        if (bouncer()->hasPermission('catalog.attributes.delete')) {
            $this->addAction([
                'icon'   => 'icon-delete',
                'title'  => trans('admin::app.catalog.attributes.index.datagrid.delete'),
                'method' => 'DELETE',
                'url'    => function ($row) {
                    return route('admin.catalog.attributes.delete', $row->id);
                },
            ]);
        }
    }

    /**
     * Prepare mass actions.
     *
     * @return void
     */
    public function prepareMassActions()
    {
        if (bouncer()->hasPermission('catalog.attributes.delete')) {
            $this->addMassAction([
                'icon'   => 'icon-delete',
                'title'  => trans('admin::app.catalog.attributes.index.datagrid.delete'),
                'method' => 'POST',
                'url'    => route('admin.catalog.attributes.mass_delete'),
            ]);
        }
    }
}
