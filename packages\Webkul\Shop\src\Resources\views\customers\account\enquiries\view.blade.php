<x-shop::layouts.account>
    <x-slot:heading>
        @lang('shop::app.customers.account.enquiries.view.title')
    </x-slot:heading>

    <div class="mx-4 flex-auto max-md:mx-6 max-sm:mx-4">
        <div class="mb-8 flex items-center max-sm:mb-5">
            <!-- Back Button -->
            <a
                class="grid"
                href="{{ route('shop.customers.account.enquiries.index') }}"
            >
                <span class="icon-arrow-left rtl:icon-arrow-right text-2xl"></span>
            </a>

            <h2 class="text-2xl font-medium max-sm:text-base ltr:ml-2.5 rtl:mr-2.5">
                @lang('shop::app.customers.account.enquiries.view.title') #{{ $enquiry->id }}
            </h2>
        </div>

        <!-- Enquiry Details Card -->
        <div class="rounded-xl border border-zinc-200 p-6">
            <div class="grid gap-6 md:grid-cols-2">
                <!-- Product Information -->
                <div>
                    <h3 class="mb-4 text-lg font-semibold text-gray-800">
                        @lang('shop::app.customers.account.enquiries.view.product-info')
                    </h3>
                    
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">Product Name:</span>
                            <p class="text-base text-gray-800">{{ $enquiry->product_name }}</p>
                        </div>
                        
                        <div>
                            <span class="text-sm font-medium text-gray-600">SKU:</span>
                            <p class="text-base text-gray-800">{{ $enquiry->product_sku }}</p>
                        </div>
                        
                        @if($enquiry->vendor_name)
                        <div>
                            <span class="text-sm font-medium text-gray-600">Vendor:</span>
                            <p class="text-base text-gray-800">{{ $enquiry->vendor_name }}</p>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Enquiry Status -->
                <div>
                    <h3 class="mb-4 text-lg font-semibold text-gray-800">
                        @lang('shop::app.customers.account.enquiries.view.enquiry-status')
                    </h3>
                    
                    <div class="space-y-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">Status:</span>
                            <span class="ml-2 inline-block rounded-full px-3 py-1 text-sm font-medium
                                @if($enquiry->status == 0) bg-yellow-100 text-yellow-800
                                @elseif($enquiry->status == 1) bg-blue-100 text-blue-800
                                @elseif($enquiry->status == 2) bg-green-100 text-green-800
                                @else bg-gray-100 text-gray-800
                                @endif
                            ">
                                @if($enquiry->status == 0) Pending
                                @elseif($enquiry->status == 1) In Progress
                                @elseif($enquiry->status == 2) Closed
                                @else Unknown
                                @endif
                            </span>
                        </div>
                        
                        <div>
                            <span class="text-sm font-medium text-gray-600">Enquired On:</span>
                            <p class="text-base text-gray-800">
                                {{ $enquiry->enquired_at ? \Carbon\Carbon::parse($enquiry->enquired_at)->format('M d, Y H:i A') : 'N/A' }}
                            </p>
                        </div>
                        
                        <div>
                            <span class="text-sm font-medium text-gray-600">Last Updated:</span>
                            <p class="text-base text-gray-800">
                                {{ \Carbon\Carbon::parse($enquiry->updated_at)->format('M d, Y H:i A') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="mt-6 flex gap-4 border-t border-gray-200 pt-6">
                <a
                    href="{{ route('shop.product_or_category.index', $enquiry->product_sku) }}"
                    class="secondary-button border-zinc-200 px-6 py-2"
                    target="_blank"
                >
                    @lang('shop::app.customers.account.enquiries.view.view-product')
                </a>
                
                <a
                    href="{{ route('shop.customers.account.enquiries.index') }}"
                    class="primary-button px-6 py-2"
                >
                    @lang('shop::app.customers.account.enquiries.view.back-to-enquiries')
                </a>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-6 rounded-xl border border-blue-200 bg-blue-50 p-4">
            <div class="flex items-start gap-3">
                <svg class="mt-0.5 h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div>
                    <h4 class="font-medium text-blue-800">Need Help?</h4>
                    <p class="mt-1 text-sm text-blue-700">
                        If you have any questions about this enquiry or need assistance, please contact our support team.
                    </p>
                </div>
            </div>
        </div>
    </div>
</x-shop::layouts.account>
