{"__meta": {"id": "01K03PMACVADCPTNJXFSM7QQD8", "datetime": "2025-07-14 11:00:54", "utime": **********.748795, "method": "GET", "uri": "/api/categories/tree", "ip": "127.0.0.1"}, "modules": {"count": 3, "modules": [{"name": "Webkul\\Category", "models": ["Webkul\\Category\\Models\\Category (1)", "Webkul\\Category\\Models\\CategoryTranslation (1)"], "views": [], "queries": [{"sql": "select `_lft`, `_rgt` from `categories` where `id` = 1 limit 1", "duration": 0.72, "duration_str": "720ms", "connection": "geniusdb"}, {"sql": "select * from `categories` where `status` = 1 and (`categories`.`_lft` between 1 and 6) order by `position` asc", "duration": 1.16, "duration_str": "1.16s", "connection": "geniusdb"}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (1)", "duration": 0.85, "duration_str": "850ms", "connection": "geniusdb"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 13.87, "duration_str": "13.87s", "connection": "geniusdb"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.46, "duration_str": "460ms", "connection": "geniusdb"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 0.8, "duration_str": "800ms", "connection": "geniusdb"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.42, "duration_str": "420ms", "connection": "geniusdb"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.49, "duration_str": "490ms", "connection": "geniusdb"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752471053.801287, "end": **********.758792, "duration": 0.9575049877166748, "duration_str": "958ms", "measures": [{"label": "Booting", "start": 1752471053.801287, "relative_start": 0, "end": **********.310864, "relative_end": **********.310864, "duration": 0.****************, "duration_str": "510ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.310882, "relative_start": 0.****************, "end": **********.758794, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "448ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.337579, "relative_start": 0.****************, "end": **********.342993, "relative_end": **********.342993, "duration": 0.005414009094238281, "duration_str": "5.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.701661, "relative_start": 0.***************, "end": **********.745356, "relative_end": **********.745356, "duration": 0.*****************, "duration_str": "43.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.020640000000000002, "accumulated_duration_str": "20.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "type": "query", "params": [], "bindings": ["127.0.0.1:8000", "http://127.0.0.1:8000", "https://127.0.0.1:8000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.37726, "duration": 0.013869999999999999, "duration_str": "13.87ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "geniusdb", "explain": null, "start_percent": 0, "width_percent": 67.2}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.397278, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 67.2, "width_percent": 2.229}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.4002142, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 69.428, "width_percent": 3.876}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.423731, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 73.304, "width_percent": 4.7}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.425623, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 78.004, "width_percent": 2.035}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.427116, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 80.039, "width_percent": 4.36}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.4289641, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 84.399, "width_percent": 2.374}, {"sql": "select `_lft`, `_rgt` from `categories` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 38}, {"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 235}, {"index": 18, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 227}, {"index": 19, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}], "start": **********.487853, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:38", "source": {"index": 14, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=38", "ajax": false, "filename": "QueryBuilder.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 86.773, "width_percent": 3.488}, {"sql": "select * from `categories` where `status` = 1 and (`categories`.`_lft` between 1 and 6) order by `position` asc", "type": "query", "params": [], "bindings": [1, 1, 6], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, {"index": 16, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 321}, {"index": 17, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 182}, {"index": 18, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CategoryController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CategoryController.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.490437, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:305", "source": {"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=305", "ajax": false, "filename": "QueryBuilder.php", "line": "305"}, "connection": "geniusdb", "explain": null, "start_percent": 90.262, "width_percent": 5.62}, {"sql": "select * from `category_translations` where `category_translations`.`category_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 321}, {"index": 22, "namespace": null, "name": "packages/Webkul/Category/src/Repositories/CategoryRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Category\\src\\Repositories\\CategoryRepository.php", "line": 182}, {"index": 23, "namespace": null, "name": "packages/Webkul/Shop/src/Http/Controllers/API/CategoryController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Controllers\\API\\CategoryController.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.520733, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=305", "ajax": false, "filename": "QueryBuilder.php", "line": "305"}, "connection": "geniusdb", "explain": null, "start_percent": 95.882, "width_percent": 4.118}]}, "models": {"data": {"Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Category\\Models\\Category": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "Webkul\\Category\\Models\\CategoryTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCategory%2Fsrc%2FModels%2FCategoryTranslation.php&line=1", "ajax": false, "filename": "CategoryTranslation.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/categories/tree", "action_name": "shop.api.categories.tree", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\CategoryController@tree", "uri": "GET api/categories/tree", "controller": "Webkul\\Shop\\Http\\Controllers\\API\\CategoryController@tree<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/categories", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FAPI%2FCategoryController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/API/CategoryController.php:48-53</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "960ms", "peak_memory": "40MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-794037081 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-794037081\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-796807551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-796807551\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1670347027 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ii9iU05mczVXSVRFT3gwd3g3TzZORnc9PSIsInZhbHVlIjoiT2FqUzVEUVk2dEludkJHZVRVZGpmV29mY2c1eFJXR2o4N0RiT3dMMU5tVk9WVVY3UElmV0hPS0Y1blpEQmlrR3FwdWlwQTNqdUZmdVRIM0ZIc1BMaWNDeVlSMWZ6Y0svR3ljY3oyL2QvSlkzakFNa1RiNm8zd2xvWWI1RjYzaWEiLCJtYWMiOiIyYzNjZmJmNjE5NDFmNDQyY2EwZTAwM2E1Y2MxN2UxNjY5MWE1N2U2OTc2NmY4YmMzMGQ3NTAxN2NjMmFjZDJjIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ii9iU05mczVXSVRFT3gwd3g3TzZORnc9PSIsInZhbHVlIjoiT2FqUzVEUVk2dEludkJHZVRVZGpmV29mY2c1eFJXR2o4N0RiT3dMMU5tVk9WVVY3UElmV0hPS0Y1blpEQmlrR3FwdWlwQTNqdUZmdVRIM0ZIc1BMaWNDeVlSMWZ6Y0svR3ljY3oyL2QvSlkzakFNa1RiNm8zd2xvWWI1RjYzaWEiLCJtYWMiOiIyYzNjZmJmNjE5NDFmNDQyY2EwZTAwM2E1Y2MxN2UxNjY5MWE1N2U2OTc2NmY4YmMzMGQ3NTAxN2NjMmFjZDJjIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6ImpaanBLb3h0WHNHYnZoc1l5dG9CNmc9PSIsInZhbHVlIjoiS3BHVVZOZVBTWCtEYzZLa25DUm9YSnZ2c2szK2tnd2VNZ1FnNldIcUVJS3R0eFRHQWQ5dVpPdjh5ek9heW1kbWVLYzJJSUMyYXNVT2pSQ2tGdjlDTnZySDNvSDBEWjJjWTRoQTdONlZ0UDcvNURyeEt1amYreTB5TUpNMnRobDYiLCJtYWMiOiJmNDU4NTk5N2EzMGU4MDc5Yzk3NGFlNmM5MTU4NDE4YTZhNzA1ODBhNmVlNmZkYTkwYTg1NGI2OTNkZjY1MzRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1670347027\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1812411588 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E96H0xbWkm53eX0bb2afQSaVSnqZa433ryTrd90r</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ZpGr9fvR8TQV04iSEkdTzFRkuRlh9oBunD04AjRv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812411588\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-449388709 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 05:30:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-449388709\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-145881429 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">E96H0xbWkm53eX0bb2afQSaVSnqZa433ryTrd90r</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-145881429\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/api/categories/tree", "action_name": "shop.api.categories.tree", "controller_action": "Webkul\\Shop\\Http\\Controllers\\API\\CategoryController@tree"}, "badge": null}}