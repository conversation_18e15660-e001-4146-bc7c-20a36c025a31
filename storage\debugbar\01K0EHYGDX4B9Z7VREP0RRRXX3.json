{"__meta": {"id": "01K0EHYGDX4B9Z7VREP0RRRXX3", "datetime": "2025-07-18 16:10:44", "utime": **********.478683, "method": "PUT", "uri": "/admin/catalog/products/edit/7", "ip": "127.0.0.1"}, "modules": {"count": 9, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\AttributeFamily (6)", "Webkul\\Attribute\\Models\\Attribute (86)"], "views": [], "queries": [{"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 7", "duration": 0.91, "duration_str": "910ms", "connection": "geniusdb"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.62, "duration_str": "620ms", "connection": "geniusdb"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1 and 1 = 1", "duration": 1.22, "duration_str": "1.22s", "connection": "geniusdb"}, {"sql": "select * from `attributes` where `code` = 'url_key'", "duration": 1.38, "duration_str": "1.38s", "connection": "geniusdb"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.89, "duration_str": "890ms", "connection": "geniusdb"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.03, "duration_str": "1.03s", "connection": "geniusdb"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.41, "duration_str": "410ms", "connection": "geniusdb"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.24, "duration_str": "1.24s", "connection": "geniusdb"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.99, "duration_str": "990ms", "connection": "geniusdb"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "duration": 0.88, "duration_str": "880ms", "connection": "geniusdb"}, {"sql": "select * from `attributes` where `code` = 'price'", "duration": 0.94, "duration_str": "940ms", "connection": "geniusdb"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "duration": 1.37, "duration_str": "1.37s", "connection": "geniusdb"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "duration": 0.61, "duration_str": "610ms", "connection": "geniusdb"}]}, {"name": "Webkul\\CatalogRule", "models": [], "views": [], "queries": [{"sql": "delete from `catalog_rule_products` where `product_id` in (7)", "duration": 0.7, "duration_str": "700ms", "connection": "geniusdb"}, {"sql": "delete from `catalog_rule_product_prices` where `product_id` in (7)", "duration": 0.36, "duration_str": "360ms", "connection": "geniusdb"}, {"sql": "select * from `catalog_rules` where (`catalog_rules`.`starts_from` <= '2025-07-18' or `catalog_rules`.`starts_from` is null) and (`catalog_rules`.`ends_till` >= '2025-07-18' or `catalog_rules`.`ends_till` is null) and `status` = 1 order by `sort_order` asc", "duration": 0.69, "duration_str": "690ms", "connection": "geniusdb"}, {"sql": "select distinct `catalog_rule_products`.*, `pav_price`.`float_value` as `price` from `catalog_rule_products` left join `products` on `catalog_rule_products`.`product_id` = `products`.`id` left join `product_attribute_values` as `pav_price` on `pav_price`.`channel` is null and `pav_price`.`locale` is null and `products`.`id` = `pav_price`.`product_id` and `pav_price`.`attribute_id` = 11 where `catalog_rule_products`.`product_id` = 7 order by `channel_id` asc, `customer_group_id` asc, `product_id` asc, `sort_order` asc, `catalog_rule_id` asc", "duration": 2.12, "duration_str": "2.12s", "connection": "geniusdb"}, {"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` = 7 and `catalog_rule_product_prices`.`product_id` is not null", "duration": 0.63, "duration_str": "630ms", "connection": "geniusdb"}]}, {"name": "Webkul\\Category", "models": [], "views": [], "queries": [{"sql": "select exists(select 1 from `category_translations` where `slug` = 'directing-the-erp-implementation' limit 1) as `exists`", "duration": 0.77, "duration_str": "770ms", "connection": "geniusdb"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (2)", "Webkul\\Core\\Models\\Locale (3)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 0.96, "duration_str": "960ms", "connection": "geniusdb"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.attribute.file_attribute_upload_size'", "duration": 0.9, "duration_str": "900ms", "connection": "geniusdb"}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.engine'", "duration": 0.85, "duration_str": "850ms", "connection": "geniusdb"}, {"sql": "select * from `locales` where `locales`.`id` = 1 limit 1", "duration": 1.03, "duration_str": "1.03s", "connection": "geniusdb"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.99, "duration_str": "990ms", "connection": "geniusdb"}, {"sql": "select * from `currencies` where `currencies`.`id` = 1 limit 1", "duration": 0.87, "duration_str": "870ms", "connection": "geniusdb"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 7", "duration": 1.8, "duration_str": "1.8s", "connection": "geniusdb"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.23, "duration_str": "1.23s", "connection": "geniusdb"}]}, {"name": "Webkul\\Customer", "models": ["Webkul\\Customer\\Models\\CustomerGroup (3)"], "views": [], "queries": [{"sql": "select * from `customer_groups`", "duration": 1.2, "duration_str": "1.2s", "connection": "geniusdb"}]}, {"name": "Webkul\\Inventory", "models": ["Webkul\\Inventory\\Models\\InventorySource (1)"], "views": [], "queries": [{"sql": "select `inventory_sources`.*, `channel_inventory_sources`.`channel_id` as `pivot_channel_id`, `channel_inventory_sources`.`inventory_source_id` as `pivot_inventory_source_id` from `inventory_sources` inner join `channel_inventory_sources` on `inventory_sources`.`id` = `channel_inventory_sources`.`inventory_source_id` where `channel_inventory_sources`.`channel_id` = 1", "duration": 0.96, "duration_str": "960ms", "connection": "geniusdb"}]}, {"name": "Webkul\\Marketing", "models": [], "views": [], "queries": [{"sql": "select * from `url_rewrites` where `entity_type` in ('category', 'product') and `request_path` = 'directing-the-erp-implementation'", "duration": 0.73, "duration_str": "730ms", "connection": "geniusdb"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (8)", "Webkul\\Product\\Models\\ProductAttributeValue (76)", "Webkul\\Product\\Models\\ProductFlat (1)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = '7' limit 1", "duration": 1.17, "duration_str": "1.17s", "connection": "geniusdb"}, {"sql": "select count(*) as aggregate from `products` where `sku` = 'Product-simple-08' and `id` <> '7'", "duration": 1.28, "duration_str": "1.28s", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'directing-the-erp-implementation'", "duration": 3.58, "duration_str": "3.58s", "connection": "geniusdb"}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "duration": 1.24, "duration_str": "1.24s", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "duration": 0.91, "duration_str": "910ms", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "duration": 1.29, "duration_str": "1.29s", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "duration": 0.93, "duration_str": "930ms", "connection": "geniusdb"}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "duration": 0.52, "duration_str": "520ms", "connection": "geniusdb"}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "duration": 0.57, "duration_str": "570ms", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "duration": 0.99, "duration_str": "990ms", "connection": "geniusdb"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 7 and `product_images`.`product_id` is not null order by `position` asc", "duration": 1.05, "duration_str": "1.05s", "connection": "geniusdb"}, {"sql": "select `id` from `product_videos` where `product_videos`.`product_id` = 7 and `product_videos`.`product_id` is not null order by `position` asc", "duration": 1, "duration_str": "1s", "connection": "geniusdb"}, {"sql": "select `id` from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 7 and `product_customer_group_prices`.`product_id` is not null", "duration": 1.27, "duration_str": "1.27s", "connection": "geniusdb"}, {"sql": "select `id` from `product_customizable_options` where `product_customizable_options`.`product_id` = 7 and `product_customizable_options`.`product_id` is not null order by `sort_order` asc", "duration": 1.38, "duration_str": "1.38s", "connection": "geniusdb"}, {"sql": "select * from `products` where `id` = 7 limit 1", "duration": 1, "duration_str": "1s", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "duration": 1.55, "duration_str": "1.55s", "connection": "geniusdb"}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "duration": 0.92, "duration_str": "920ms", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "duration": 1.17, "duration_str": "1.17s", "connection": "geniusdb"}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 7", "duration": 0.67, "duration_str": "670ms", "connection": "geniusdb"}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 7", "duration": 0.56, "duration_str": "560ms", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "duration": 1.28, "duration_str": "1.28s", "connection": "geniusdb"}, {"sql": "select * from `product_flat` where (`product_id` = 7 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 1.02, "duration_str": "1.02s", "connection": "geniusdb"}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 7", "duration": 0.92, "duration_str": "920ms", "connection": "geniusdb"}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 7", "duration": 0.86, "duration_str": "860ms", "connection": "geniusdb"}, {"sql": "select * from `products` where `id` in (7) order by FIELD(id, 7)", "duration": 0.65, "duration_str": "650ms", "connection": "geniusdb"}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 7 and `product_inventory_indices`.`product_id` is not null", "duration": 0.56, "duration_str": "560ms", "connection": "geniusdb"}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` = 7 and `product_inventories`.`product_id` is not null", "duration": 0.41, "duration_str": "410ms", "connection": "geniusdb"}, {"sql": "select * from `product_ordered_inventories` where `product_ordered_inventories`.`product_id` = 7 and `product_ordered_inventories`.`product_id` is not null", "duration": 0.45, "duration_str": "450ms", "connection": "geniusdb"}, {"sql": "select * from `products` where `id` in (7) order by FIELD(id, 7)", "duration": 0.91, "duration_str": "910ms", "connection": "geniusdb"}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 7 and `product_price_indices`.`product_id` is not null", "duration": 0.93, "duration_str": "930ms", "connection": "geniusdb"}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 7 and `product_customer_group_prices`.`product_id` is not null", "duration": 0.65, "duration_str": "650ms", "connection": "geniusdb"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "duration": 1.17, "duration_str": "1.17s", "connection": "geniusdb"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.78, "duration_str": "780ms", "connection": "geniusdb"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.57, "duration_str": "570ms", "connection": "geniusdb"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.76, "duration_str": "760ms", "connection": "geniusdb"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.71, "duration_str": "710ms", "connection": "geniusdb"}]}]}, "messages": {"count": 1, "messages": [{"message": "[16:10:44] LOG.info: Bouncer: Authentication check {\n    \"active_guard\": \"admin\",\n    \"user_id\": 1,\n    \"user_email\": \"<EMAIL>\",\n    \"user_status\": 1,\n    \"user_role_id\": 1,\n    \"route\": \"admin.catalog.products.update\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.006619, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.375977, "end": **********.490512, "duration": 1.114534854888916, "duration_str": "1.11s", "measures": [{"label": "Booting", "start": **********.375977, "relative_start": 0, "end": **********.880785, "relative_end": **********.880785, "duration": 0.****************, "duration_str": "505ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.880813, "relative_start": 0.***************, "end": **********.490516, "relative_end": 4.0531158447265625e-06, "duration": 0.****************, "duration_str": "610ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.907401, "relative_start": 0.****************, "end": **********.910844, "relative_end": **********.910844, "duration": 0.003443002700805664, "duration_str": "3.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.475304, "relative_start": 1.****************, "end": **********.475995, "relative_end": **********.475995, "duration": 0.0006911754608154297, "duration_str": "691μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 80, "nb_statements": 80, "nb_visible_statements": 80, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11495999999999999, "accumulated_duration_str": "115ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.966731, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 0, "width_percent": 1.2}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.9745598, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 1.2, "width_percent": 0.678}, {"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "type": "query", "params": [], "bindings": ["127.0.0.1:8000", "http://127.0.0.1:8000", "https://127.0.0.1:8000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.987274, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "geniusdb", "explain": null, "start_percent": 1.879, "width_percent": 0.835}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.9911602, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 2.714, "width_percent": 0.713}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 51}], "start": **********.9933739, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 3.427, "width_percent": 0.496}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 19}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.001386, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "geniusdb", "explain": null, "start_percent": 3.923, "width_percent": 0.661}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 109}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 71}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.0090349, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "admin:109", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 109}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=109", "ajax": false, "filename": "Bouncer.php", "line": "109"}, "connection": "geniusdb", "explain": null, "start_percent": 4.584, "width_percent": 0.618}, {"sql": "select * from `core_config` where `code` = 'catalog.products.attribute.file_attribute_upload_size'", "type": "query", "params": [], "bindings": ["catalog.products.attribute.file_attribute_upload_size"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.02231, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 5.202, "width_percent": 0.783}, {"sql": "select * from `products` where `products`.`id` = '7' limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.047202, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "geniusdb", "explain": null, "start_percent": 5.985, "width_percent": 1.018}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 546}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 419}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.06654, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 7.002, "width_percent": 0.792}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 419}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.069138, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 7.794, "width_percent": 0.539}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1 and 1 = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 555}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 419}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.070889, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:555", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=555", "ajax": false, "filename": "AbstractType.php", "line": "555"}, "connection": "geniusdb", "explain": null, "start_percent": 8.333, "width_percent": 1.061}, {"sql": "select count(*) as aggregate from `products` where `sku` = 'Product-simple-08' and `id` <> '7'", "type": "query", "params": [], "bindings": ["Product-simple-08", "7"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.080792, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "geniusdb", "explain": null, "start_percent": 9.395, "width_percent": 1.113}, {"sql": "select exists(select 1 from `category_translations` where `slug` = 'directing-the-erp-implementation' limit 1) as `exists`", "type": "query", "params": [], "bindings": ["directing-the-erp-implementation"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 103}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 77}, {"index": 13, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}], "start": **********.0850608, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ProductCategoryUniqueSlug.php:103", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FProductCategoryUniqueSlug.php&line=103", "ajax": false, "filename": "ProductCategoryUniqueSlug.php", "line": "103"}, "connection": "geniusdb", "explain": null, "start_percent": 10.508, "width_percent": 0.67}, {"sql": "select * from `core_config` where `code` = 'catalog.products.search.engine'", "type": "query", "params": [], "bindings": ["catalog.products.search.engine"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 321}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 19, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 320}], "start": **********.088309, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 11.178, "width_percent": 0.739}, {"sql": "select * from `attributes` where `code` = 'url_key'", "type": "query", "params": [], "bindings": ["url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 134}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 192}], "start": **********.093736, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "geniusdb", "explain": null, "start_percent": 11.917, "width_percent": 1.2}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = 'directing-the-erp-implementation'", "type": "query", "params": [], "bindings": [3, "directing-the-erp-implementation"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 136}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 192}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}], "start": **********.097831, "duration": 0.0035800000000000003, "duration_str": "3.58ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 13.118, "width_percent": 3.114}, {"sql": "select * from `locales` where `locales`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 215}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 163}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 192}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}], "start": **********.115446, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 16.232, "width_percent": 0.896}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 42}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.129131, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "geniusdb", "explain": null, "start_percent": 17.128, "width_percent": 1.079}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 515}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 397}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}], "start": **********.133306, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 18.206, "width_percent": 0.774}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 515}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 397}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}], "start": **********.135668, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "geniusdb", "explain": null, "start_percent": 18.981, "width_percent": 0.896}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 433}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 399}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}], "start": **********.1394322, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 19.876, "width_percent": 0.861}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 437}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 399}, {"index": 24, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}, {"index": 29, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}], "start": **********.144942, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 20.738, "width_percent": 0.792}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 438}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 399}, {"index": 23, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1477928, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Product.php:438", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 438}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=438", "ajax": false, "filename": "Product.php", "line": "438"}, "connection": "geniusdb", "explain": null, "start_percent": 21.529, "width_percent": 1.122}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 438}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 399}, {"index": 24, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 48}, {"index": 29, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.151767, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Product.php:438", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 438}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=438", "ajax": false, "filename": "Product.php", "line": "438"}, "connection": "geniusdb", "explain": null, "start_percent": 22.651, "width_percent": 0.809}, {"sql": "select * from `url_rewrites` where `entity_type` in ('category', 'product') and `request_path` = 'directing-the-erp-implementation'", "type": "query", "params": [], "bindings": ["category", "product", "directing-the-erp-implementation"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 53}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.154103, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 23.46, "width_percent": 0.635}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 79}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.156123, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Repository.php:152", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=152", "ajax": false, "filename": "Repository.php", "line": "152"}, "connection": "geniusdb", "explain": null, "start_percent": 24.095, "width_percent": 0.452}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.158978, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "geniusdb", "explain": null, "start_percent": 24.548, "width_percent": 0.496}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.161155, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 25.043, "width_percent": 0.357}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.162846, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "geniusdb", "explain": null, "start_percent": 25.4, "width_percent": 1.079}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 63}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}], "start": **********.166219, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 26.479, "width_percent": 0.861}, {"sql": "insert into `product_attribute_values` (`attribute_id`, `boolean_value`, `channel`, `date_value`, `datetime_value`, `float_value`, `integer_value`, `locale`, `product_id`, `text_value`, `unique_id`) values (9, null, null, null, null, null, null, 'en', 7, '<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success.</p>', 'en|7|9'), (10, null, null, null, null, null, null, 'en', 7, '<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success. In adherence to its time-tested framework defined in this book, your organization will be able to commit to the principles and practices leading to fruitful implementations. In addition, this ERP book offers practical guidance to aid executive leadership in doing the right things along the ERP journey. Thus, you get an innovative and fast-track yet comprehensive approach to ERP implementation success. Directing the ERP Implementation: A Best Practice Guide offers in-depth explanations into project plans, generating requirements, and obtaining results-oriented commitment. Also, a detailed practical deployment framework essential for success alongside useful tools is included to help you grasp the ERP concepts. This ERP book covers every aspect, from software selection and integration to common difficulties found in ERP.</p>', 'en|7|10'), (1, null, null, null, null, null, null, null, 7, 'Product-simple-08', '7|1'), (2, null, null, null, null, null, null, 'en', 7, 'Directing the ERP Implementation', 'en|7|2'), (3, null, null, null, null, null, null, 'en', 7, 'directing-the-erp-implementation', 'en|7|3'), (27, null, null, null, null, null, null, null, 7, '', '7|27'), (16, null, null, null, null, null, null, 'en', 7, '', 'en|7|16'), (17, null, null, null, null, null, null, 'en', 7, '', 'en|7|17'), (18, null, null, null, null, null, null, 'en', 7, '', 'en|7|18'), (11, null, null, null, null, '999', null, null, 7, null, '7|11'), (12, null, null, null, null, null, null, null, 7, null, '7|12'), (13, null, null, null, null, null, null, null, 7, null, '7|13'), (14, null, 'default', null, null, null, null, null, 7, null, 'default|7|14'), (15, null, 'default', null, null, null, null, null, 7, null, 'default|7|15'), (5, 1, null, null, null, null, null, null, 7, null, '7|5'), (6, 0, null, null, null, null, null, null, 7, null, '7|6'), (7, 1, null, null, null, null, null, null, 7, null, '7|7'), (8, 1, 'default', null, null, null, null, null, 7, null, 'default|7|8'), (26, 1, null, null, null, null, null, null, 7, null, '7|26')", "type": "query", "params": [], "bindings": [9, null, null, null, null, null, null, "en", 7, "<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success.</p>", "en|7|9", 10, null, null, null, null, null, null, "en", 7, "<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success. In adherence to its time-tested framework defined in this book, your organization will be able to commit to the principles and practices leading to fruitful implementations. In addition, this ERP book offers practical guidance to aid executive leadership in doing the right things along the ERP journey. Thus, you get an innovative and fast-track yet comprehensive approach to ERP implementation success. Directing the ERP Implementation: A Best Practice Guide offers in-depth explanations into project plans, generating requirements, and obtaining results-oriented commitment. Also, a detailed practical deployment framework essential for success alongside useful tools is included to help you grasp the ERP concepts. This ERP book covers every aspect, from software selection and integration to common difficulties found in ERP.</p>", "en|7|10", 1, null, null, null, null, null, null, null, 7, "Product-simple-08", "7|1", 2, null, null, null, null, null, null, "en", 7, "Directing the ERP Implementation", "en|7|2", 3, null, null, null, null, null, null, "en", 7, "directing-the-erp-implementation", "en|7|3", 27, null, null, null, null, null, null, null, 7, "", "7|27", 16, null, null, null, null, null, null, "en", 7, "", "en|7|16", 17, null, null, null, null, null, null, "en", 7, "", "en|7|17", 18, null, null, null, null, null, null, "en", 7, "", "en|7|18", 11, null, null, null, null, "999", null, null, 7, null, "7|11", 12, null, null, null, null, null, null, null, 7, null, "7|12", 13, null, null, null, null, null, null, null, 7, null, "7|13", 14, null, "default", null, null, null, null, null, 7, null, "default|7|14", 15, null, "default", null, null, null, null, null, 7, null, "default|7|15", 5, 1, null, null, null, null, null, null, 7, null, "7|5", 6, 0, null, null, null, null, null, null, 7, null, "7|6", 7, 1, null, null, null, null, null, null, 7, null, "7|7", 8, 1, "default", null, null, null, null, null, 7, null, "default|7|8", 26, 1, null, null, null, null, null, null, 7, null, "7|26"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 137}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.1732628, "duration": 0.00517, "duration_str": "5.17ms", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:137", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 137}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=137", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "137"}, "connection": "geniusdb", "explain": null, "start_percent": 27.34, "width_percent": 4.497}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 173}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.180376, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:173", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=173", "ajax": false, "filename": "AbstractType.php", "line": "173"}, "connection": "geniusdb", "explain": null, "start_percent": 31.837, "width_percent": 1.966}, {"sql": "select * from `product_categories` where `product_categories`.`product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 179}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.18665, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:179", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=179", "ajax": false, "filename": "AbstractType.php", "line": "179"}, "connection": "geniusdb", "explain": null, "start_percent": 33.803, "width_percent": 0.748}, {"sql": "select * from `product_up_sells` where `product_up_sells`.`parent_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 181}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.189039, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:181", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=181", "ajax": false, "filename": "AbstractType.php", "line": "181"}, "connection": "geniusdb", "explain": null, "start_percent": 34.551, "width_percent": 0.644}, {"sql": "select * from `product_cross_sells` where `product_cross_sells`.`parent_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 183}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1919272, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:183", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=183", "ajax": false, "filename": "AbstractType.php", "line": "183"}, "connection": "geniusdb", "explain": null, "start_percent": 35.195, "width_percent": 0.557}, {"sql": "select * from `product_relations` where `product_relations`.`parent_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 185}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.193783, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "AbstractType.php:185", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=185", "ajax": false, "filename": "AbstractType.php", "line": "185"}, "connection": "geniusdb", "explain": null, "start_percent": 35.752, "width_percent": 0.339}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 7 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 189}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.195514, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "geniusdb", "explain": null, "start_percent": 36.091, "width_percent": 0.913}, {"sql": "insert into `product_images` (`type`, `path`, `product_id`, `position`) values ('images', 'product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp', 7, 1)", "type": "query", "params": [], "bindings": ["images", "product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp", 7, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 69}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 189}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}], "start": **********.248235, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "geniusdb", "explain": null, "start_percent": 37.004, "width_percent": 2.209}, {"sql": "select `id` from `product_videos` where `product_videos`.`product_id` = 7 and `product_videos`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 191}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.2573571, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "geniusdb", "explain": null, "start_percent": 39.214, "width_percent": 0.87}, {"sql": "select `id` from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 7 and `product_customer_group_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 193}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 72}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.260449, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "ProductCustomerGroupPriceRepository.php:24", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductCustomerGroupPriceRepository.php&line=24", "ajax": false, "filename": "ProductCustomerGroupPriceRepository.php", "line": "24"}, "connection": "geniusdb", "explain": null, "start_percent": 40.084, "width_percent": 1.105}, {"sql": "select `id` from `product_customizable_options` where `product_customizable_options`.`product_id` = 7 and `product_customizable_options`.`product_id` is not null order by `sort_order` asc", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomizableOptionRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomizableOptionRepository.php", "line": 41}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Simple.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Type\\Simple.php", "line": 78}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 81}, {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.264794, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ProductCustomizableOptionRepository.php:41", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomizableOptionRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomizableOptionRepository.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductCustomizableOptionRepository.php&line=41", "ajax": false, "filename": "ProductCustomizableOptionRepository.php", "line": "41"}, "connection": "geniusdb", "explain": null, "start_percent": 41.188, "width_percent": 1.2}, {"sql": "select * from `products` where `id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 83}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2682061, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:83", "source": {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductRepository.php&line=83", "ajax": false, "filename": "ProductRepository.php", "line": "83"}, "connection": "geniusdb", "explain": null, "start_percent": 42.389, "width_percent": 0.87}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 83}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.27133, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:83", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductRepository.php&line=83", "ajax": false, "filename": "ProductRepository.php", "line": "83"}, "connection": "geniusdb", "explain": null, "start_percent": 43.259, "width_percent": 0.861}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 83}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.274712, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:83", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductRepository.php&line=83", "ajax": false, "filename": "ProductRepository.php", "line": "83"}, "connection": "geniusdb", "explain": null, "start_percent": 44.12, "width_percent": 1.348}, {"sql": "select * from `products` where `products`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 93}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 63}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 102}], "start": **********.307622, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:110", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=110", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "110"}, "connection": "geniusdb", "explain": null, "start_percent": 45.468, "width_percent": 0.8}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 63}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 102}], "start": **********.311262, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:110", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=110", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "110"}, "connection": "geniusdb", "explain": null, "start_percent": 46.268, "width_percent": 0.765}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 63}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesModels.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesModels.php", "line": 93}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\CallQueuedHandler.php", "line": 63}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Jobs\\Job.php", "line": 102}], "start": **********.314069, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "SerializesAndRestoresModelIdentifiers.php:110", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/SerializesAndRestoresModelIdentifiers.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\SerializesAndRestoresModelIdentifiers.php", "line": 110}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FSerializesAndRestoresModelIdentifiers.php&line=110", "ajax": false, "filename": "SerializesAndRestoresModelIdentifiers.php", "line": "110"}, "connection": "geniusdb", "explain": null, "start_percent": 47.034, "width_percent": 1.018}, {"sql": "delete from `catalog_rule_products` where `product_id` in (7)", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProduct.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProduct.php", "line": 100}, {"index": 13, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleIndex.php", "line": 121}, {"index": 14, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleIndex.php", "line": 88}, {"index": 15, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Jobs/UpdateCreateProductIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Jobs\\UpdateCreateProductIndex.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.324129, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "CatalogRuleProduct.php:100", "source": {"index": 12, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProduct.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProduct.php", "line": 100}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCatalogRule%2Fsrc%2FHelpers%2FCatalogRuleProduct.php&line=100", "ajax": false, "filename": "CatalogRuleProduct.php", "line": "100"}, "connection": "geniusdb", "explain": null, "start_percent": 48.051, "width_percent": 0.609}, {"sql": "delete from `catalog_rule_product_prices` where `product_id` in (7)", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 12, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProductPrice.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProductPrice.php", "line": 150}, {"index": 13, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleIndex.php", "line": 123}, {"index": 14, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleIndex.php", "line": 88}, {"index": 15, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Jobs/UpdateCreateProductIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Jobs\\UpdateCreateProductIndex.php", "line": 34}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.32638, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "CatalogRuleProductPrice.php:150", "source": {"index": 12, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProductPrice.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProductPrice.php", "line": 150}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCatalogRule%2Fsrc%2FHelpers%2FCatalogRuleProductPrice.php&line=150", "ajax": false, "filename": "CatalogRuleProductPrice.php", "line": "150"}, "connection": "geniusdb", "explain": null, "start_percent": 48.66, "width_percent": 0.313}, {"sql": "select * from `catalog_rules` where (`catalog_rules`.`starts_from` <= '2025-07-18' or `catalog_rules`.`starts_from` is null) and (`catalog_rules`.`ends_till` >= '2025-07-18' or `catalog_rules`.`ends_till` is null) and `status` = 1 order by `sort_order` asc", "type": "query", "params": [], "bindings": ["2025-07-18", "2025-07-18", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleIndex.php", "line": 143}, {"index": 18, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleIndex.php", "line": 90}, {"index": 19, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Jobs/UpdateCreateProductIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Jobs\\UpdateCreateProductIndex.php", "line": 34}], "start": **********.328025, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 48.974, "width_percent": 0.6}, {"sql": "select * from `attributes` where `code` = 'price'", "type": "query", "params": [], "bindings": ["price"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProduct.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProduct.php", "line": 217}, {"index": 19, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProduct.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProduct.php", "line": 186}], "start": **********.33018, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "geniusdb", "explain": null, "start_percent": 49.574, "width_percent": 0.818}, {"sql": "select distinct `catalog_rule_products`.*, `pav_price`.`float_value` as `price` from `catalog_rule_products` left join `products` on `catalog_rule_products`.`product_id` = `products`.`id` left join `product_attribute_values` as `pav_price` on `pav_price`.`channel` is null and `pav_price`.`locale` is null and `products`.`id` = `pav_price`.`product_id` and `pav_price`.`attribute_id` = 11 where `catalog_rule_products`.`product_id` = 7 order by `channel_id` asc, `customer_group_id` asc, `product_id` asc, `sort_order` asc, `catalog_rule_id` asc", "type": "query", "params": [], "bindings": [11, 7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 344}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 389}, {"index": 18, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProduct.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProduct.php", "line": 203}, {"index": 19, "namespace": null, "name": "packages/Webkul/CatalogRule/src/Helpers/CatalogRuleProductPrice.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\CatalogRule\\src\\Helpers\\CatalogRuleProductPrice.php", "line": 39}], "start": **********.333212, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:344", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=344", "ajax": false, "filename": "BaseRepository.php", "line": "344"}, "connection": "geniusdb", "explain": null, "start_percent": 50.391, "width_percent": 1.844}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 114}, {"index": 18, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 87}, {"index": 19, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 61}], "start": **********.338527, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 52.236, "width_percent": 0.583}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 135}, {"index": 18, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 88}, {"index": 19, "namespace": null, "name": "packages/Webkul/FPC/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\FPC\\src\\Listeners\\Product.php", "line": 61}], "start": **********.340556, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 52.818, "width_percent": 0.487}, {"sql": "select * from `currencies` where `currencies`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 22, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 390}, {"index": 23, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 433}, {"index": 24, "namespace": null, "name": "packages/Webkul/FPC/src/Hasher/DefaultHasher.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\FPC\\src\\Hasher\\DefaultHasher.php", "line": 29}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-responsecache/src/Hasher/DefaultHasher.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\spatie\\laravel-responsecache\\src\\Hasher\\DefaultHasher.php", "line": 18}], "start": **********.350215, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 20, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 53.305, "width_percent": 0.757}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'geniusdb' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 60}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 763}], "start": **********.356558, "duration": 0.01945, "duration_str": "19.45ms", "memory": 0, "memory_str": null, "filename": "Flat.php:60", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=60", "ajax": false, "filename": "Flat.php", "line": "60"}, "connection": "geniusdb", "explain": null, "start_percent": 54.062, "width_percent": 16.919}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 207}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 132}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 52}], "start": **********.377652, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "geniusdb", "explain": null, "start_percent": 70.981, "width_percent": 1.192}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 52}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 155}], "start": **********.3811278, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 72.173, "width_percent": 1.566}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 52}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 155}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3844168, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Flat.php:140", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=140", "ajax": false, "filename": "Flat.php", "line": "140"}, "connection": "geniusdb", "explain": null, "start_percent": 73.739, "width_percent": 1.113}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 144}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 52}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 155}], "start": **********.389331, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 74.852, "width_percent": 1.07}, {"sql": "select * from `product_flat` where (`product_id` = 7 and `channel` = 'default' and `locale` = 'en') limit 1", "type": "query", "params": [], "bindings": [7, "default", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 52}, {"index": 28, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 155}], "start": **********.392838, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "geniusdb", "explain": null, "start_percent": 75.922, "width_percent": 0.887}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'geniusdb' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 145}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 52}, {"index": 29, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 155}], "start": **********.3954768, "duration": 0.00868, "duration_str": "8.68ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 21, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "geniusdb", "explain": null, "start_percent": 76.809, "width_percent": 7.55}, {"sql": "update `product_flat` set `product_number` = '', `name` = 'Directing the ERP Implementation', `short_description` = '<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success.</p>', `description` = '<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success. In adherence to its time-tested framework defined in this book, your organization will be able to commit to the principles and practices leading to fruitful implementations. In addition, this ERP book offers practical guidance to aid executive leadership in doing the right things along the ERP journey. Thus, you get an innovative and fast-track yet comprehensive approach to ERP implementation success. Directing the ERP Implementation: A Best Practice Guide offers in-depth explanations into project plans, generating requirements, and obtaining results-oriented commitment. Also, a detailed practical deployment framework essential for success alongside useful tools is included to help you grasp the ERP concepts. This ERP book covers every aspect, from software selection and integration to common difficulties found in ERP.</p>', `url_key` = 'directing-the-erp-implementation', `new` = 1, `featured` = 0, `status` = 1, `meta_title` = '', `meta_keywords` = '', `meta_description` = '', `price` = '999.0000', `visible_individually` = 1, `product_flat`.`updated_at` = '2025-07-18 16:10:44' where `id` = 7", "type": "query", "params": [], "bindings": ["", "Directing the ERP Implementation", "<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success.</p>", "<p>This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success. In adherence to its time-tested framework defined in this book, your organization will be able to commit to the principles and practices leading to fruitful implementations. In addition, this ERP book offers practical guidance to aid executive leadership in doing the right things along the ERP journey. Thus, you get an innovative and fast-track yet comprehensive approach to ERP implementation success. Directing the ERP Implementation: A Best Practice Guide offers in-depth explanations into project plans, generating requirements, and obtaining results-oriented commitment. Also, a detailed practical deployment framework essential for success alongside useful tools is included to help you grasp the ERP concepts. This ERP book covers every aspect, from software selection and integration to common difficulties found in ERP.</p>", "directing-the-erp-implementation", 1, 0, 1, "", "", "", "999.0000", 1, "2025-07-18 16:10:44", 7], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 113}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 52}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 155}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4103808, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "Flat.php:184", "source": {"index": 14, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Flat.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Flat.php", "line": 184}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FFlat.php&line=184", "ajax": false, "filename": "Flat.php", "line": "184"}, "connection": "geniusdb", "explain": null, "start_percent": 84.36, "width_percent": 2.644}, {"sql": "select * from `product_bundle_option_products` where `product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 124}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 103}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 54}], "start": **********.416235, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 87.004, "width_percent": 0.8}, {"sql": "select * from `product_grouped_products` where `associated_product_id` = 7", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 145}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 104}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Listeners/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Listeners\\Product.php", "line": 54}], "start": **********.418759, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "geniusdb", "explain": null, "start_percent": 87.804, "width_percent": 0.748}, {"sql": "select * from `products` where `id` in (7) order by FIELD(id, 7)", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.423609, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "UpdateCreateInventoryIndex.php:44", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FJobs%2FUpdateCreateInventoryIndex.php&line=44", "ajax": false, "filename": "UpdateCreateInventoryIndex.php", "line": "44"}, "connection": "geniusdb", "explain": null, "start_percent": 88.553, "width_percent": 0.565}, {"sql": "select * from `product_inventory_indices` where `product_inventory_indices`.`product_id` = 7 and `product_inventory_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 120}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.4284298, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 89.118, "width_percent": 0.487}, {"sql": "select `inventory_sources`.*, `channel_inventory_sources`.`channel_id` as `pivot_channel_id`, `channel_inventory_sources`.`inventory_source_id` as `pivot_inventory_source_id` from `inventory_sources` inner join `channel_inventory_sources` on `inventory_sources`.`id` = `channel_inventory_sources`.`inventory_source_id` where `channel_inventory_sources`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 180}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 125}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}], "start": **********.4311068, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 89.605, "width_percent": 0.835}, {"sql": "select * from `product_inventories` where `product_inventories`.`product_id` = 7 and `product_inventories`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 184}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 125}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}], "start": **********.433452, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 90.44, "width_percent": 0.357}, {"sql": "select * from `product_ordered_inventories` where `product_ordered_inventories`.`product_id` = 7 and `product_ordered_inventories`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 125}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}], "start": **********.4349291, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 90.797, "width_percent": 0.391}, {"sql": "insert into `product_inventory_indices` (`channel_id`, `product_id`, `qty`) values (1, 7, 0)", "type": "query", "params": [], "bindings": [1, 7, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 146}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreateInventoryIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreateInventoryIndex.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4363, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Inventory.php:146", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Inventory.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Inventory.php", "line": 146}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FInventory.php&line=146", "ajax": false, "filename": "Inventory.php", "line": "146"}, "connection": "geniusdb", "explain": null, "start_percent": 91.188, "width_percent": 1.479}, {"sql": "select * from `products` where `id` in (7) order by FIELD(id, 7)", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreatePriceIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreatePriceIndex.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.439883, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "UpdateCreatePriceIndex.php:44", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreatePriceIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreatePriceIndex.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FJobs%2FUpdateCreatePriceIndex.php&line=44", "ajax": false, "filename": "UpdateCreatePriceIndex.php", "line": "44"}, "connection": "geniusdb", "explain": null, "start_percent": 92.667, "width_percent": 0.792}, {"sql": "select * from `customer_groups`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, {"index": 17, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 212}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price.php", "line": 234}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price.php", "line": 148}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}], "start": **********.447011, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:346", "source": {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 346}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=346", "ajax": false, "filename": "BaseRepository.php", "line": "346"}, "connection": "geniusdb", "explain": null, "start_percent": 93.459, "width_percent": 1.044}, {"sql": "select * from `product_price_indices` where `product_price_indices`.`product_id` = 7 and `product_price_indices`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price.php", "line": 149}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreatePriceIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreatePriceIndex.php", "line": 46}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.4502928, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 94.502, "width_percent": 0.809}, {"sql": "select * from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 7 and `product_customer_group_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 62}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 165}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 109}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 91}], "start": **********.452678, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 95.311, "width_percent": 0.565}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 515}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 397}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 168}, {"index": 26, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 109}], "start": **********.4549508, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 95.877, "width_percent": 0.531}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 7 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 437}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 399}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 168}, {"index": 25, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 109}], "start": **********.457613, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 96.407, "width_percent": 1.018}, {"sql": "select * from `catalog_rule_product_prices` where `catalog_rule_product_prices`.`product_id` = 7 and `catalog_rule_product_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 214}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 111}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price/AbstractType.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price\\AbstractType.php", "line": 91}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price.php", "line": 158}], "start": **********.460385, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Product.php:405", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 405}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=405", "ajax": false, "filename": "Product.php", "line": "405"}, "connection": "geniusdb", "explain": null, "start_percent": 97.425, "width_percent": 0.548}, {"sql": "insert into `product_price_indices` (`channel_id`, `customer_group_id`, `max_price`, `min_price`, `product_id`, `regular_max_price`, `regular_min_price`) values (1, 1, '999.0000', '999.0000', 7, '999.0000', '999.0000'), (1, 2, '999.0000', '999.0000', 7, '999.0000', '999.0000'), (1, 3, '999.0000', '999.0000', 7, '999.0000', '999.0000')", "type": "query", "params": [], "bindings": [1, 1, "999.0000", "999.0000", 7, "999.0000", "999.0000", 1, 2, "999.0000", "999.0000", 7, "999.0000", "999.0000", 1, 3, "999.0000", "999.0000", 7, "999.0000", "999.0000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price.php", "line": 180}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/AbstractIndexer.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\AbstractIndexer.php", "line": 66}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Jobs/UpdateCreatePriceIndex.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Jobs\\UpdateCreatePriceIndex.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.464998, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "Price.php:180", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Helpers/Indexers/Price.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Product\\src\\Helpers\\Indexers\\Price.php", "line": 180}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FHelpers%2FIndexers%2FPrice.php&line=180", "ajax": false, "filename": "Price.php", "line": "180"}, "connection": "geniusdb", "explain": null, "start_percent": 97.973, "width_percent": 2.027}]}, "models": {"data": {"Webkul\\Attribute\\Models\\Attribute": {"value": 86, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductAttributeValue": {"value": 76, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Customer\\Models\\CustomerGroup": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCustomer%2Fsrc%2FModels%2FCustomerGroup.php&line=1", "ajax": false, "filename": "CustomerGroup.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductFlat": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductFlat.php&line=1", "ajax": false, "filename": "ProductFlat.php", "line": "?"}}, "Webkul\\Inventory\\Models\\InventorySource": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInventory%2Fsrc%2FModels%2FInventorySource.php&line=1", "ajax": false, "filename": "InventorySource.php", "line": "?"}}}, "count": 189, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/admin/catalog/products/edit/7", "action_name": "admin.catalog.products.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update", "uri": "PUT admin/catalog/products/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=149\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/catalog/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=149\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php:149-160</a>", "middleware": "web, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, vendor.subscription", "duration": "1.12s", "peak_memory": "48MB", "response": "Redirect to http://127.0.0.1:8000/admin/catalog/products", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-2144132210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2144132210\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-736625468 data-indent-pad=\"  \"><span class=sf-dump-note>array:23</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJUCCakpz5FJypKnWmk6NNg0t28x5bEHTlp7QX5K</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Product-simple-08</span>\"\n  \"<span class=sf-dump-key>product_number</span>\" => \"\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Directing the ERP Implementation</span>\"\n  \"<span class=sf-dump-key>url_key</span>\" => \"<span class=sf-dump-str title=\"32 characters\">directing-the-erp-implementation</span>\"\n  \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"141 characters\">&lt;p&gt;This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success.&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"972 characters\">&lt;p&gt;This ERP book delivers proven roadmaps alongside best practices with high improvement possibilities for system implementation success. In adherence to its time-tested framework defined in this book, your organization will be able to commit to the principles and practices leading to fruitful implementations. In addition, this ERP book offers practical guidance to aid executive leadership in doing the right things along the ERP journey. Thus, you get an innovative and fast-track yet comprehensive approach to ERP implementation success. Directing the ERP Implementation: A Best Practice Guide offers in-depth explanations into project plans, generating requirements, and obtaining results-oriented commitment. Also, a detailed practical deployment framework essential for success alongside useful tools is included to help you grasp the ERP concepts. This ERP book covers every aspect, from software selection and integration to common difficulties found in ERP.&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>meta_title</span>\" => \"\"\n  \"<span class=sf-dump-key>meta_keywords</span>\" => \"\"\n  \"<span class=sf-dump-key>meta_description</span>\" => \"\"\n  \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">999</span>\"\n  \"<span class=sf-dump-key>cost</span>\" => \"\"\n  \"<span class=sf-dump-key>special_price</span>\" => \"\"\n  \"<span class=sf-dump-key>special_price_from</span>\" => \"\"\n  \"<span class=sf-dump-key>special_price_to</span>\" => \"\"\n  \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>visible_individually</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>guest_checkout</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>channels</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736625468\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-560079895 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">32307</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryBTHQ2TKUtnCNI8Go</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"51 characters\">http://127.0.0.1:8000/admin/catalog/products/edit/7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlhCK2xpMGhXdlhmYWQ2SHg1Vy80UEE9PSIsInZhbHVlIjoiZGE3YnJSZG4razZDN21qeE5oYkRMYk1URHp3NFBvRXNaclAxV2tPYlhXcTlKQUJ4aWpyZG40ZUxBRWdURE5POUZONkVncmp3NnZjWHdSaHhEdm5RV2FqcFVJWU5EVlBKZ3ZybTVKMCtzMllTclRtdmlNSVV1UXNjU3lXaGpXVlgiLCJtYWMiOiIxMWNjYmQ5MTQ2MTM2Mzc5YmQ3NjIyODk3NDk0OGVhMzdkZmQzMTQzZDllN2RjMDYzMjU3NmU4NmNjZWNmMGI0IiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IkdaeW9zZ3c1SnFzNitac1l6ZUNXd2c9PSIsInZhbHVlIjoiR25OY2c1aWF0T1lQYVZzNkZWczc3ZmN2RGxBV0o4anBCYlViajB1b2JjSWFmR3VuZDlWSEJDNXNDUUZLWkp1RDlHMDF0SG1uM2xDUTZVN0EwRGdTekM0STlyTHpmdE9SaDZpZlJaUHdOajhMSzA1NjRFZUxHV0ZzbTR0dlBqeXQiLCJtYWMiOiJmMGU0MDM0NDU4NzJkMGIwZDNlMTMwN2Q2OTVhZjY5ODRhZGU5MmUxNTBkNjljYTIzZDdlOGJhNDI1MTNjMmE3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-560079895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2106347941 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJUCCakpz5FJypKnWmk6NNg0t28x5bEHTlp7QX5K</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HJdxYxKgTHdCK0qiJuK5VOmpNdZNQDwgFHzuVJTn</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106347941\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-553340752 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 10:40:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553340752\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1901475366 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">PJUCCakpz5FJypKnWmk6NNg0t28x5bEHTlp7QX5K</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"51 characters\">http://127.0.0.1:8000/admin/catalog/products/edit/7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Product updated successfully</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901475366\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://127.0.0.1:8000/admin/catalog/products/edit/7", "action_name": "admin.catalog.products.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update"}, "badge": "302 Found"}}