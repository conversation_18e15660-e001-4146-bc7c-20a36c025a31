<?php
    use Webkul\Admin\Services\VendorSubscriptionService;
    
    $subscriptionService = app(VendorSubscriptionService::class);
    $vendor = auth()->guard('vendor')->user();
    $subscription = null;
    $accessInfo = null;
    
    if ($vendor) {
        $subscription = $subscriptionService->getActiveSubscription($vendor->id);
        $accessInfo = $subscriptionService->checkCatalogAccess($vendor->id);
    }
?>

<?php if($vendor && $subscription): ?>
    <div class="mb-6 rounded-lg border <?php echo e($accessInfo['allowed'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'); ?> p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="mr-3">
                    <?php if($accessInfo['allowed']): ?>
                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                            <i class="icon-check text-green-600"></i>
                        </div>
                    <?php else: ?>
                        <div class="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                            <i class="icon-warning text-red-600"></i>
                        </div>
                    <?php endif; ?>
                </div>
                <div>
                    <h3 class="text-lg font-semibold <?php echo e($accessInfo['allowed'] ? 'text-green-800' : 'text-red-800'); ?>">
                        <?php echo e($subscription->plan_name); ?>

                    </h3>
                    <p class="text-sm <?php echo e($accessInfo['allowed'] ? 'text-green-600' : 'text-red-600'); ?>">
                        Status: <?php echo e($subscriptionService->getSubscriptionStatus($subscription)); ?>

                    </p>
                </div>
            </div>
            
            <div class="text-right">
                <?php if($subscription->expires_at): ?>
                    <?php
                        $daysRemaining = $subscriptionService->getDaysRemaining($subscription);
                    ?>
                    <p class="text-sm font-medium <?php echo e($accessInfo['allowed'] ? 'text-green-800' : 'text-red-800'); ?>">
                        <?php if($daysRemaining > 0): ?>
                            <?php echo e($daysRemaining); ?> days remaining
                        <?php else: ?>
                            Expired
                        <?php endif; ?>
                    </p>
                    <p class="text-xs <?php echo e($accessInfo['allowed'] ? 'text-green-600' : 'text-red-600'); ?>">
                        Expires: <?php echo e(\Carbon\Carbon::parse($subscription->expires_at)->format('M d, Y')); ?>

                    </p>
                <?php else: ?>
                    <p class="text-sm font-medium text-green-800">Lifetime Access</p>
                <?php endif; ?>
            </div>
        </div>
        
        <?php if(!$accessInfo['allowed']): ?>
            <div class="mt-3 flex items-center justify-between rounded-md bg-white p-3">
                <p class="text-sm text-red-700"><?php echo e($accessInfo['message']); ?></p>
                <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="rounded bg-red-600 px-3 py-1 text-sm text-white hover:bg-red-700">
                    <?php if($subscriptionService->getSubscriptionStatus($subscription) === 'Expired'): ?>
                        Renew Plan
                    <?php else: ?>
                        View Plans
                    <?php endif; ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
<?php elseif($vendor): ?>
    <div class="mb-6 rounded-lg border border-orange-200 bg-orange-50 p-4">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="mr-3">
                    <div class="flex h-8 w-8 items-center justify-center rounded-full bg-orange-100">
                        <i class="icon-info text-orange-600"></i>
                    </div>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-orange-800">No Active Subscription</h3>
                    <p class="text-sm text-orange-600">Subscribe to a plan to access catalog features</p>
                </div>
            </div>
            
            <a href="<?php echo e(route('admin.subscriptions.index')); ?>" class="rounded bg-orange-600 px-4 py-2 text-sm text-white hover:bg-orange-700">
                Browse Plans
            </a>
        </div>
    </div>
<?php endif; ?>
<?php /**PATH D:\venkat clone repo\geniusmart\mart\digitalmartgenius\packages\Webkul\Admin\src/resources/views/components/subscription-status.blade.php ENDPATH**/ ?>