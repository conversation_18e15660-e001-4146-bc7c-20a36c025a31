<?php
use Illuminate\Support\Facades\Route;
use Webkul\Admin\Http\Controllers\Enquiry\EnquiryController;

/**
 * Enquiry routes.
 */

Route::controller(EnquiryController::class)->prefix('enquiries')->group(function () {
    Route::get('', 'index')->name('admin.enquiries.index');
    Route::get('/list', 'list')->name('admin.enquiries.list');
    Route::post('', 'store')->name('admin.enquiries.store');
    Route::get('{id}', 'show')->name('admin.enquiries.show');
    Route::put('{id}', 'update')->name('admin.enquiries.update');
    Route::delete('{id}', 'destroy')->name('admin.enquiries.destroy');

    // Mass actions
    Route::post('mass-delete', 'massDelete')->name('admin.enquiries.mass_delete');
    Route::post('mass-update-status', 'massUpdateStatus')->name('admin.enquiries.mass_update_status');
});

/**
 * Vendor Enquiry routes.
 */
Route::controller(\Webkul\Admin\Http\Controllers\Enquiry\VendorEnquiryController::class)
    ->prefix('vendor/enquiries')
    ->middleware('auth:vendor')
    ->group(function () {
        Route::get('', 'index')->name('admin.enquiries.vendor.index');
        Route::get('stats', 'getStats')->name('admin.enquiries.vendor.stats');
        Route::get('{id}', 'show')->name('admin.enquiries.vendor.show');
        Route::post('{id}/status', 'updateStatus')->name('admin.enquiries.vendor.update-status');
    });