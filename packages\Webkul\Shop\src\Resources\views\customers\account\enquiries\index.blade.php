<x-shop::layouts.account>
    <x-slot:heading>
        @lang('shop::app.customers.account.enquiries.title')
    </x-slot:heading>

    <div class="mx-4 flex-auto max-md:mx-6 max-sm:mx-4">
        <div class="mb-8 flex items-center max-sm:mb-5">
            <!-- Back Button -->
            <a
                class="grid md:hidden"
                href="{{ route('shop.customers.account.index') }}"
            >
                <span class="icon-arrow-left rtl:icon-arrow-right text-2xl"></span>
            </a>

            <h2 class="text-2xl font-medium max-sm:text-base ltr:ml-2.5 md:ltr:ml-0 rtl:mr-2.5 md:rtl:mr-0">
                @lang('shop::app.customers.account.enquiries.title')
            </h2>
        </div>

        {!! view_render_event('bagisto.shop.customers.account.enquiries.list.before') !!}

        <!-- For Desktop View -->
        <div class="max-md:hidden">
            <x-shop::datagrid :src="route('shop.customers.account.enquiries.index')" />
        </div>

        <!-- For Mobile View -->
        <div class="md:hidden">
            <x-shop::datagrid :src="route('shop.customers.account.enquiries.index')">
                <!-- Datagrid Header -->
                <template #header="{
                    isLoading,
                    available,
                    applied,
                    selectAll,
                    sort,
                    performAction
                }">
                    <div class="hidden"></div>
                </template>

                <template #body="{
                    isLoading,
                    available,
                    applied,
                    selectAll,
                    sort,
                    performAction
                }">
                    <template v-if="isLoading">
                        <x-shop::shimmer.datagrid.table.body />
                    </template>

                    <template v-else>
                        <div
                            class="row grid grid-cols-1 gap-y-6"
                            v-for="record in available.records"
                        >
                            <!-- Enquiry Card -->
                            <div class="flex justify-between gap-x-4 rounded-xl border border-zinc-200 p-5">
                                <div class="grid gap-y-1.5">
                                    <!-- Product Name -->
                                    <p class="text-base font-medium">
                                        @{{ record.product_name }}
                                    </p>

                                    <!-- Product SKU -->
                                    <p class="text-sm text-zinc-500">
                                        SKU: @{{ record.product_sku }}
                                    </p>

                                    <!-- Status -->
                                    <p class="text-sm">
                                        <span class="font-medium">Status:</span>
                                        <span 
                                            class="inline-block rounded-full px-2 py-1 text-xs font-medium"
                                            :class="{
                                                'bg-yellow-100 text-yellow-800': record.status === 'Pending',
                                                'bg-blue-100 text-blue-800': record.status === 'In Progress',
                                                'bg-green-100 text-green-800': record.status === 'Closed'
                                            }"
                                        >
                                            @{{ record.status }}
                                        </span>
                                    </p>

                                    <!-- Vendor -->
                                    <p class="text-sm text-zinc-500" v-if="record.vendor">
                                        <span class="font-medium">Vendor:</span> @{{ record.vendor }}
                                    </p>

                                    <!-- Date -->
                                    <p class="text-sm text-zinc-500">
                                        <span class="font-medium">Enquired:</span>
                                        @{{ new Date(record.enquired_at).toLocaleDateString() }}
                                    </p>
                                </div>

                                <!-- Actions -->
                                <div class="flex flex-col items-end gap-2">
                                    <a
                                        :href="`{{ route('shop.customers.account.enquiries.view', '') }}/${record.id}`"
                                        class="text-sm text-blue-600 hover:text-blue-800"
                                    >
                                        View Details
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Empty State -->
                        <div
                            class="grid h-96 place-content-center place-items-center gap-3 text-center"
                            v-if="! available.records.length"
                        >
                            <img
                                class="h-20 w-20"
                                src="{{ bagisto_asset('images/empty-placeholders/enquiries.svg') }}"
                                alt="@lang('shop::app.customers.account.enquiries.empty')"
                            />

                            <div class="grid gap-1">
                                <p class="text-base font-semibold">
                                    @lang('shop::app.customers.account.enquiries.empty')
                                </p>

                                <p class="text-zinc-500">
                                    @lang('shop::app.customers.account.enquiries.empty-description')
                                </p>
                            </div>

                            <a
                                href="{{ route('shop.home.index') }}"
                                class="secondary-button border-zinc-200 px-11 py-3"
                            >
                                @lang('shop::app.customers.account.enquiries.start-shopping')
                            </a>
                        </div>
                    </template>
                </template>
            </x-shop::datagrid>
        </div>

        {!! view_render_event('bagisto.shop.customers.account.enquiries.list.after') !!}
    </div>
</x-shop::layouts.account>
