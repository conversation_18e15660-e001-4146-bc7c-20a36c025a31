<?php

namespace Webkul\Shop\Http\Controllers\Customer;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Event;
use Webkul\Customer\Services\VendorService;
use Webkul\Shop\Http\Controllers\Controller;
use Webkul\Shop\Http\Requests\Customer\LoginRequest;

class SessionController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @param  \Webkul\Customer\Services\VendorService  $vendorService
     */
    public function __construct(
        protected VendorService $vendorService
    ) {}

    /**
     * Display the resource.
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\View\View
     */
    public function index()
    {
        if (auth()->guard('customer')->check()) {
            return redirect()->route('shop.home.index');
        }

        return view('shop::customers.sign-in');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function store(LoginRequest $loginRequest)
    {
        $email = $loginRequest->get('email');
        $password = $loginRequest->get('password');
        $loginType = $loginRequest->get('login_type', 'customer');

        // Handle vendor login only if explicitly selected
        if ($loginType === 'vendor') {
            $vendorLoginResult = $this->vendorService->canLogin($email, $password);

            if ($vendorLoginResult['success']) {
                // Login as vendor
                auth()->guard('vendor')->login($vendorLoginResult['vendor']);

                Event::dispatch('vendor.after.login', $vendorLoginResult['vendor']);

                // Keep vendors on shop frontend after login
                return redirect()->route('shop.home.index')
                    ->with('success', 'Welcome back! You are now logged in as a vendor.');
            } elseif ($vendorLoginResult['vendor'] !== null) {
                // Email belongs to a vendor but login failed due to status/verification
                session()->flash('error', $vendorLoginResult['message']);
                return redirect()->back();
            } else {
                // User selected vendor login but email doesn't belong to any vendor
                session()->flash('error', 'No vendor account found with this email address.');
                return redirect()->back();
            }
        }

        // For customer login, check if email belongs to a vendor and block it
        $vendorExists = $this->vendorService->findByEmail($email);
        if ($vendorExists) {
            session()->flash('error', 'This email is registered as a vendor. Please use the Vendor Login option.');
            return redirect()->back();
        }

        // If not a vendor, try customer login
        if (! auth()->guard('customer')->attempt($loginRequest->only(['email', 'password']))) {
            session()->flash('error', trans('shop::app.customers.login-form.invalid-credentials'));

            return redirect()->back();
        }

        if (! auth()->guard('customer')->user()->status) {
            auth()->guard('customer')->logout();

            session()->flash('warning', trans('shop::app.customers.login-form.not-activated'));

            return redirect()->back();
        }

        if (! auth()->guard('customer')->user()->is_verified) {
            session()->flash('info', trans('shop::app.customers.login-form.verify-first'));

            Cookie::queue(Cookie::make('enable-resend', 'true', 1));

            Cookie::queue(Cookie::make('email-for-resend', $loginRequest->get('email'), 1));

            auth()->guard('customer')->logout();

            return redirect()->back();
        }

        /**
         * Event passed to prepare cart after login.
         */
        Event::dispatch('customer.after.login', auth()->guard()->user());

        if (core()->getConfigData('customer.settings.login_options.redirected_to_page') == 'account') {
            return redirect()->route('shop.customers.account.profile.index');
        }

        return redirect()->route('shop.home.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        auth()->guard('customer')->logout();

        Event::dispatch('customer.after.logout', $id);

        return redirect()->route('shop.home.index');
    }

    /**
     * Check if email belongs to a vendor.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkEmailType(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
        ]);

        $email = $request->input('email');
        $vendor = $this->vendorService->findByEmail($email);

        return response()->json([
            'is_vendor' => $vendor !== null,
            'message' => $vendor ? 'This email is registered as a vendor. Please use the Vendor Login option.' : 'Email is available for customer login.',
        ]);
    }
}
