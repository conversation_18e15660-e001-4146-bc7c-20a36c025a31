{"__meta": {"id": "01K0EVTC6SRMGK2KWDBVTHD1QE", "datetime": "2025-07-18 19:03:14", "utime": **********.842138, "method": "GET", "uri": "/cache/original/product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.468912, "end": **********.857001, "duration": 0.3880891799926758, "duration_str": "388ms", "measures": [{"label": "Booting", "start": **********.468912, "relative_start": 0, "end": **********.806927, "relative_end": **********.806927, "duration": 0.***************, "duration_str": "338ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.806943, "relative_start": 0.****************, "end": **********.857004, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "50.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.828663, "relative_start": 0.*****************, "end": **********.835037, "relative_end": **********.835037, "duration": 0.006373882293701172, "duration_str": "6.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.83966, "relative_start": 0.*****************, "end": **********.839828, "relative_end": **********.839828, "duration": 0.00016808509826660156, "duration_str": "168μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.839849, "relative_start": 0.****************, "end": **********.839865, "relative_end": **********.839865, "duration": 1.5974044799804688e-05, "duration_str": "16μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/original/product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "389ms", "peak_memory": "34MB", "response": "image/webp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-631488375 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-631488375\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1030305292 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1030305292\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-45894692 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"54 characters\">http://127.0.0.1:8000/directing-the-erp-implementation</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6Ik5jQlZ6a1hLSHpPMWVOS3JpYVZmbXc9PSIsInZhbHVlIjoiSWpXSDAxeVJLLzdua1VXNFpSb3hPR1BySTE0VWMzbGNkRkExWXlvdFhaWU92RG5pRU5rcnhGcER1aG0vRmhLcXk5WFplNlQ5dXVTNE1EdlF6d3JMUFlLZm51RDR1Z3U1WS9SZXlONy9jZlpON09aN0FobTFDVEMxYkRudHFicEQiLCJtYWMiOiI4ZmE2MjNlMjcyMTQwNzUyMzQ3YmZkNGM5ODI0MTBhNTE2OTA3OTRlMjlhNjdmZjdiMzJlNzc3YjRmYjViODI5IiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6Ilh4cURvSGZTbGtlUmxSb3FpYklkWFE9PSIsInZhbHVlIjoiQ0M0NEpFMVR5RGwyNzA5cjAzMUp3TkJjMEdBWVRXY2x0YUtnWkJ6VlgveXlWRGJaaGdsc0o5U1FsWVRDdzVwSmRKQmtrQUU4VjNvUlhkZW5SUHN6dUU0V2J1RDJPcjhHS1JweVVrVkNCRUtURlIyeVM4dkZxTDNFeWRxWjg2bUkiLCJtYWMiOiIwOWVjNWZhZjU3MTFjZjVmZTZkYzYwYzcxMTgzMDhhNzRlNjRlZjAwYTdkYTBiODNjOWRjODRhM2JkNmZhNzczIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-45894692\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1304597873 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5jQlZ6a1hLSHpPMWVOS3JpYVZmbXc9PSIsInZhbHVlIjoiSWpXSDAxeVJLLzdua1VXNFpSb3hPR1BySTE0VWMzbGNkRkExWXlvdFhaWU92RG5pRU5rcnhGcER1aG0vRmhLcXk5WFplNlQ5dXVTNE1EdlF6d3JMUFlLZm51RDR1Z3U1WS9SZXlONy9jZlpON09aN0FobTFDVEMxYkRudHFicEQiLCJtYWMiOiI4ZmE2MjNlMjcyMTQwNzUyMzQ3YmZkNGM5ODI0MTBhNTE2OTA3OTRlMjlhNjdmZjdiMzJlNzc3YjRmYjViODI5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilh4cURvSGZTbGtlUmxSb3FpYklkWFE9PSIsInZhbHVlIjoiQ0M0NEpFMVR5RGwyNzA5cjAzMUp3TkJjMEdBWVRXY2x0YUtnWkJ6VlgveXlWRGJaaGdsc0o5U1FsWVRDdzVwSmRKQmtrQUU4VjNvUlhkZW5SUHN6dUU0V2J1RDJPcjhHS1JweVVrVkNCRUtURlIyeVM4dkZxTDNFeWRxWjg2bUkiLCJtYWMiOiIwOWVjNWZhZjU3MTFjZjVmZTZkYzYwYzcxMTgzMDhhNzRlNjRlZjAwYTdkYTBiODNjOWRjODRhM2JkNmZhNzczIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304597873\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2013740186 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">image/webp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">34094</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">71538bf0697e2128db4bdbb8b1f77549</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 18 Jul 2025 13:33:14 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2013740186\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-808946389 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-808946389\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/original/product/7/dGvdlzaT0Mt3XaU5485XvBxyyH8HNgOXLiIT9OV8.webp", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}