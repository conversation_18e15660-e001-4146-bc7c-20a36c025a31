<!-- SEO Meta Content -->
@push('meta')
    <meta name="description" content="@lang('shop::app.customers.login-form.page-title')"/>

    <meta name="keywords" content="@lang('shop::app.customers.login-form.page-title')"/>
@endPush

<x-shop::layouts
    :has-header="false"
    :has-feature="false"
    :has-footer="false"
>
    <!-- Page Title -->
    <x-slot:title>
        @lang('shop::app.customers.login-form.page-title')
    </x-slot>

    <div class="container mt-20 max-1180:px-5 max-md:mt-12">
        {!! view_render_event('bagisto.shop.customers.login.logo.before') !!}

        <!-- Company Logo -->
        <div class="flex items-center gap-x-14 max-[1180px]:gap-x-9">
            <a
                href="{{ route('shop.home.index') }}"
                class="m-[0_auto_20px_auto]"
                aria-label="@lang('shop::app.customers.login-form.bagisto')"
            >
                <img
                    src="{{ core()->getCurrentChannel()->logo_url ?? bagisto_asset('images/logo.svg') }}"
                    alt="{{ config('app.name') }}"
                    width="131"
                    height="29"
                >
            </a>
        </div>

        {!! view_render_event('bagisto.shop.customers.login.logo.after') !!}

        <!-- Form Container -->
        <div class="m-auto w-full max-w-[870px] rounded-xl border border-zinc-200 p-16 px-[90px] max-md:px-8 max-md:py-8 max-sm:border-none max-sm:p-0">
            <h1 class="font-dmserif text-4xl max-md:text-3xl max-sm:text-xl">
                @lang('shop::app.customers.login-form.page-title')
            </h1>

            <p class="mt-4 text-xl text-zinc-500 max-sm:mt-0 max-sm:text-sm">
                @lang('shop::app.customers.login-form.form-login-text')
            </p>

            {!! view_render_event('bagisto.shop.customers.login.before') !!}

            <div class="mt-14 rounded max-sm:mt-8">
                <!-- Login Type Selection -->
                <div class="mb-8">
                    <div class="flex gap-4 max-sm:flex-col">
                        <!-- Customer Login -->
                        <div class="flex-1">
                            <input
                                type="radio"
                                id="customer-login"
                                name="login_type"
                                value="customer"
                                class="peer hidden"
                                checked
                                onchange="switchLoginType('customer')"
                            />
                            <label
                                for="customer-login"
                                class="login-type-label active flex cursor-pointer items-center justify-center rounded-xl border-2 border-navyBlue bg-navyBlue text-white p-6 text-center shadow-lg max-sm:p-4"
                            >
                                <div>
                                    <div class="mb-2 flex justify-center">
                                        <span class="icon-customer login-type-icon text-2xl max-sm:text-xl"></span>
                                    </div>
                                    <div class="text-lg font-semibold max-sm:text-base">Customer Login</div>
                                    <div class="text-sm opacity-75 max-sm:text-xs">Shop and manage your orders</div>
                                </div>
                            </label>
                        </div>

                        <!-- Vendor Login -->
                        <div class="flex-1">
                            <input
                                type="radio"
                                id="vendor-login"
                                name="login_type"
                                value="vendor"
                                class="peer hidden"
                                onchange="switchLoginType('vendor')"
                            />
                            <label
                                for="vendor-login"
                                class="login-type-label inactive flex cursor-pointer items-center justify-center rounded-xl border-2 border-zinc-200 bg-white text-gray-700 p-6 text-center max-sm:p-4"
                            >
                                <div>
                                    <div class="mb-2 flex justify-center">
                                        <span class="icon-store login-type-icon text-2xl max-sm:text-xl"></span>
                                    </div>
                                    <div class="text-lg font-semibold max-sm:text-base">Vendor Login</div>
                                    <div class="text-sm opacity-75 max-sm:text-xs">Manage your business and products</div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <x-shop::form id="login-form" :action="route('shop.customer.session.create')">
                    <!-- Hidden field to track login type -->
                    <input type="hidden" name="login_type" id="login_type_field" value="customer" />

                    {!! view_render_event('bagisto.shop.customers.login_form_controls.before') !!}

                    <!-- Email -->
                    <x-shop::form.control-group>
                        <x-shop::form.control-group.label class="required text-base font-medium text-gray-800 max-sm:text-sm">
                            @lang('shop::app.customers.login-form.email')
                        </x-shop::form.control-group.label>

                        <x-shop::form.control-group.control
                            type="email"
                            class="w-full rounded-xl border border-zinc-300 px-6 py-4 text-base transition-all duration-300 focus:border-navyBlue focus:ring-2 focus:ring-navyBlue focus:ring-opacity-20 max-md:py-3 max-sm:py-2 max-sm:text-sm"
                            name="email"
                            rules="required|email"
                            value=""
                            :label="trans('shop::app.customers.login-form.email')"
                            placeholder="<EMAIL>"
                            :aria-label="trans('shop::app.customers.login-form.email')"
                            aria-required="true"
                        />

                        <x-shop::form.control-group.error control-name="email" />
                    </x-shop::form.control-group>

                    <!-- Password -->
                    <x-shop::form.control-group>
                        <x-shop::form.control-group.label class="required text-base font-medium text-gray-800 max-sm:text-sm">
                            @lang('shop::app.customers.login-form.password')
                        </x-shop::form.control-group.label>

                        <x-shop::form.control-group.control
                            type="password"
                            class="w-full rounded-xl border border-zinc-300 px-6 py-4 text-base transition-all duration-300 focus:border-navyBlue focus:ring-2 focus:ring-navyBlue focus:ring-opacity-20 max-md:py-3 max-sm:py-2 max-sm:text-sm"
                            id="password"
                            name="password"
                            rules="required|min:6"
                            value=""
                            :label="trans('shop::app.customers.login-form.password')"
                            :placeholder="trans('shop::app.customers.login-form.password')"
                            :aria-label="trans('shop::app.customers.login-form.password')"
                            aria-required="true"
                        />

                        <x-shop::form.control-group.error control-name="password" />
                    </x-shop::form.control-group>

                    <div class="flex justify-between items-center max-sm:flex-col max-sm:gap-4">
                        <div class="flex select-none items-center gap-2">
                            <input
                                type="checkbox"
                                id="show-password"
                                class="peer hidden"
                                onchange="switchVisibility()"
                            />

                            <label
                                class="icon-uncheck peer-checked:icon-check-box cursor-pointer text-2xl text-navyBlue peer-checked:text-navyBlue transition-colors duration-200 max-sm:text-xl"
                                for="show-password"
                            ></label>

                            <label
                                class="cursor-pointer select-none text-base text-zinc-600 hover:text-navyBlue transition-colors duration-200 max-sm:text-sm ltr:pl-0 rtl:pr-0"
                                for="show-password"
                            >
                                @lang('shop::app.customers.login-form.show-password')
                            </label>
                        </div>

                        <div class="block">
                            <a
                                href="{{ route('shop.customers.forgot_password.create') }}"
                                class="cursor-pointer text-base text-navyBlue hover:text-darkBlue font-medium transition-colors duration-200 max-sm:text-sm"
                            >
                                <span>
                                    @lang('shop::app.customers.login-form.forgot-pass')
                                </span>
                            </a>
                        </div>
                    </div>

                    <!-- Captcha -->
                    @if (core()->getConfigData('customer.captcha.credentials.status'))
                        <div class="mt-5 flex">
                            {!! \Webkul\Customer\Facades\Captcha::render() !!}
                        </div>
                    @endif

                    <!-- Submit Button -->
                    <div class="mt-8 flex flex-wrap items-center gap-9 max-sm:justify-center max-sm:gap-5 max-sm:text-center">
                        <button
                            class="primary-button m-0 mx-auto block w-full max-w-[374px] rounded-2xl px-11 py-4 text-center text-base font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105 max-md:max-w-full max-md:rounded-lg max-md:py-3 max-sm:py-3 ltr:ml-0 rtl:mr-0"
                            type="submit"
                        >
                            @lang('shop::app.customers.login-form.button-title')
                        </button>

                        {!! view_render_event('bagisto.shop.customers.login_form_controls.after') !!}
                    </div>
                </x-shop::form>
            </div>

            {!! view_render_event('bagisto.shop.customers.login.after') !!}

            <div class="mt-8 text-center">
                <p class="font-medium text-zinc-600 max-sm:text-sm">
                    @lang('shop::app.customers.login-form.new-customer')
                    <a
                        class="text-navyBlue hover:text-darkBlue font-semibold transition-colors duration-200 underline decoration-2 underline-offset-2"
                        href="{{ route('shop.customers.register.index') }}"
                    >
                        @lang('shop::app.customers.login-form.create-your-account')
                    </a>
                </p>
            </div>
        </div>

        <p class="mb-4 mt-8 text-center text-xs text-zinc-500">
            @lang('shop::app.customers.login-form.footer', ['current_year'=> date('Y') ])
        </p>
    </div>

    @push('scripts')
        {!! \Webkul\Customer\Facades\Captcha::renderJS() !!}

        <script>
            function switchVisibility() {
                let passwordField = document.getElementById("password");

                passwordField.type = passwordField.type === "password"
                    ? "text"
                    : "password";
            }

            function switchLoginType(type) {
                const loginForm = document.getElementById('login-form');
                const loginTypeField = document.getElementById('login_type_field');

                loginTypeField.value = type;

                // Get the labels for visual styling
                const customerLabel = document.querySelector('label[for="customer-login"]');
                const vendorLabel = document.querySelector('label[for="vendor-login"]');

                // Reset both labels to inactive state
                customerLabel.classList.remove('active');
                customerLabel.classList.add('inactive');

                vendorLabel.classList.remove('active');
                vendorLabel.classList.add('inactive');

                // Apply active styling to selected type
                if (type === 'vendor') {
                    vendorLabel.classList.remove('inactive');
                    vendorLabel.classList.add('active');
                    loginForm.action = "{{ route('shop.customer.session.create') }}";
                } else {
                    customerLabel.classList.remove('inactive');
                    customerLabel.classList.add('active');
                    loginForm.action = "{{ route('shop.customer.session.create') }}";
                }
            }

            // Initialize login type styling on page load
            document.addEventListener('DOMContentLoaded', function() {
                // Ensure customer login is selected by default
                switchLoginType('customer');
            });
        </script>

        <style>
            /* Enhanced login type switcher styling */
            .login-type-label {
                transition: all 0.3s ease-in-out !important;
                border-width: 2px !important;
            }

            .login-type-label:hover {
                border-color: #060C3B !important;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
                transform: translateY(-1px);
            }

            .login-type-label.active {
                border-color: #060C3B !important;
                background-color: #060C3B !important;
                color: white !important;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
                transform: translateY(-2px);
            }

            .login-type-label.inactive {
                border-color: #d1d5db !important;
                background-color: white !important;
                color: #374151 !important;
                box-shadow: none !important;
                transform: translateY(0);
            }

            /* Icon styling */
            .login-type-icon {
                transition: all 0.3s ease-in-out;
            }

            .login-type-label.active .login-type-icon {
                transform: scale(1.1);
            }
        </style>
    @endpush
</x-shop::layouts>
