{"__meta": {"id": "01K04FFZMVAHQ5GR2JKQQTSDVJ", "datetime": "2025-07-14 18:15:27", "utime": **********.06831, "method": "GET", "uri": "/customer/login", "ip": "127.0.0.1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\Locale (1)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "duration": 26.36, "duration_str": "26.36s", "connection": "geniusdb"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.71, "duration_str": "710ms", "connection": "geniusdb"}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "duration": 1.23, "duration_str": "1.23s", "connection": "geniusdb"}]}, {"name": "Webkul\\User", "models": [], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.48, "duration_str": "480ms", "connection": "geniusdb"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.74, "duration_str": "740ms", "connection": "geniusdb"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.183141, "end": **********.082698, "duration": 0.8995571136474609, "duration_str": "900ms", "measures": [{"label": "Booting", "start": **********.183141, "relative_start": 0, "end": **********.707043, "relative_end": **********.707043, "duration": 0.****************, "duration_str": "524ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.707083, "relative_start": 0.****************, "end": **********.082701, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "376ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.745835, "relative_start": 0.****************, "end": **********.754969, "relative_end": **********.754969, "duration": 0.*****************, "duration_str": "9.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.866922, "relative_start": 0.****************, "end": **********.065825, "relative_end": **********.065825, "duration": 0.*****************, "duration_str": "199ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: shop::customers.sign-in", "start": **********.869509, "relative_start": 0.****************, "end": **********.869509, "relative_end": **********.869509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.881134, "relative_start": 0.****************, "end": **********.881134, "relative_end": **********.881134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.882637, "relative_start": 0.6994960308074951, "end": **********.882637, "relative_end": **********.882637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.884847, "relative_start": 0.7017059326171875, "end": **********.884847, "relative_end": **********.884847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.885435, "relative_start": 0.702294111251831, "end": **********.885435, "relative_end": **********.885435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.label", "start": **********.885887, "relative_start": 0.7027459144592285, "end": **********.885887, "relative_end": **********.885887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.control", "start": **********.886295, "relative_start": 0.7031540870666504, "end": **********.886295, "relative_end": **********.886295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.error", "start": **********.88724, "relative_start": 0.7040989398956299, "end": **********.88724, "relative_end": **********.88724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.control-group.index", "start": **********.887636, "relative_start": 0.7044949531555176, "end": **********.887636, "relative_end": **********.887636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_login::shop.customers.session.social-links", "start": **********.89134, "relative_start": 0.7081990242004395, "end": **********.89134, "relative_end": **********.89134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_login::icons.facebook", "start": **********.905425, "relative_start": 0.7222840785980225, "end": **********.905425, "relative_end": **********.905425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_login::icons.twitter", "start": **********.922401, "relative_start": 0.7392599582672119, "end": **********.922401, "relative_end": **********.922401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_login::icons.google", "start": **********.937377, "relative_start": 0.7542359828948975, "end": **********.937377, "relative_end": **********.937377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: social_login::icons.github", "start": **********.962345, "relative_start": 0.7792038917541504, "end": **********.962345, "relative_end": **********.962345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.form.index", "start": **********.962904, "relative_start": 0.7797629833221436, "end": **********.962904, "relative_end": **********.962904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.layouts.index", "start": **********.001061, "relative_start": 0.8179199695587158, "end": **********.001061, "relative_end": **********.001061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.index", "start": **********.039254, "relative_start": 0.8561129570007324, "end": **********.039254, "relative_end": **********.039254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.04003, "relative_start": 0.856889009475708, "end": **********.04003, "relative_end": **********.04003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.flash-group.item", "start": **********.040891, "relative_start": 0.8577499389648438, "end": **********.040891, "relative_end": **********.040891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: shop::components.modal.confirm", "start": **********.042303, "relative_start": 0.8591620922088623, "end": **********.042303, "relative_end": **********.042303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core::blade.tracer.style", "start": **********.051894, "relative_start": 0.8687529563903809, "end": **********.051894, "relative_end": **********.051894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: paypal::checkout.onepage.paypal-smart-button", "start": **********.052523, "relative_start": 0.8693819046020508, "end": **********.052523, "relative_end": **********.052523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 41025280, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "shop::customers.sign-in", "param_count": null, "params": [], "start": **********.869482, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/customers/sign-in.blade.phpshop::customers.sign-in", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcustomers%2Fsign-in.blade.php&line=1", "ajax": false, "filename": "sign-in.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.label", "param_count": null, "params": [], "start": **********.881107, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/label.blade.phpshop::components.form.control-group.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.882596, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.884823, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.index", "param_count": null, "params": [], "start": **********.88541, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/index.blade.phpshop::components.form.control-group.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.label", "param_count": null, "params": [], "start": **********.885865, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/label.blade.phpshop::components.form.control-group.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.control", "param_count": null, "params": [], "start": **********.886274, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/control.blade.phpshop::components.form.control-group.control", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.error", "param_count": null, "params": [], "start": **********.887191, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/error.blade.phpshop::components.form.control-group.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}}, {"name": "shop::components.form.control-group.index", "param_count": null, "params": [], "start": **********.887615, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/control-group/index.blade.phpshop::components.form.control-group.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Fcontrol-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "social_login::shop.customers.session.social-links", "param_count": null, "params": [], "start": **********.891305, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\SocialLogin\\src/resources/views/shop/customers/session/social-links.blade.phpsocial_login::shop.customers.session.social-links", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FSocialLogin%2Fsrc%2FResources%2Fviews%2Fshop%2Fcustomers%2Fsession%2Fsocial-links.blade.php&line=1", "ajax": false, "filename": "social-links.blade.php", "line": "?"}}, {"name": "social_login::icons.facebook", "param_count": null, "params": [], "start": **********.90538, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\SocialLogin\\src/resources/views/icons/facebook.blade.phpsocial_login::icons.facebook", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FSocialLogin%2Fsrc%2FResources%2Fviews%2Ficons%2Ffacebook.blade.php&line=1", "ajax": false, "filename": "facebook.blade.php", "line": "?"}}, {"name": "social_login::icons.twitter", "param_count": null, "params": [], "start": **********.922376, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\SocialLogin\\src/resources/views/icons/twitter.blade.phpsocial_login::icons.twitter", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FSocialLogin%2Fsrc%2FResources%2Fviews%2Ficons%2Ftwitter.blade.php&line=1", "ajax": false, "filename": "twitter.blade.php", "line": "?"}}, {"name": "social_login::icons.google", "param_count": null, "params": [], "start": **********.937354, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\SocialLogin\\src/resources/views/icons/google.blade.phpsocial_login::icons.google", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FSocialLogin%2Fsrc%2FResources%2Fviews%2Ficons%2Fgoogle.blade.php&line=1", "ajax": false, "filename": "google.blade.php", "line": "?"}}, {"name": "social_login::icons.github", "param_count": null, "params": [], "start": **********.962316, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\SocialLogin\\src/resources/views/icons/github.blade.phpsocial_login::icons.github", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FSocialLogin%2Fsrc%2FResources%2Fviews%2Ficons%2Fgithub.blade.php&line=1", "ajax": false, "filename": "github.blade.php", "line": "?"}}, {"name": "shop::components.form.index", "param_count": null, "params": [], "start": **********.962879, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/form/index.blade.phpshop::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::components.layouts.index", "param_count": null, "params": [], "start": **********.001033, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/layouts/index.blade.phpshop::components.layouts.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Flayouts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::components.flash-group.index", "param_count": null, "params": [], "start": **********.039199, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/index.blade.phpshop::components.flash-group.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.040007, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "shop::components.flash-group.item", "param_count": null, "params": [], "start": **********.040857, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/flash-group/item.blade.phpshop::components.flash-group.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fflash-group%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "shop::components.modal.confirm", "param_count": null, "params": [], "start": **********.042261, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src/resources/views/components/modal/confirm.blade.phpshop::components.modal.confirm", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FResources%2Fviews%2Fcomponents%2Fmodal%2Fconfirm.blade.php&line=1", "ajax": false, "filename": "confirm.blade.php", "line": "?"}}, {"name": "core::blade.tracer.style", "param_count": null, "params": [], "start": **********.05187, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src/resources/views/blade/tracer/style.blade.phpcore::blade.tracer.style", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FResources%2Fviews%2Fblade%2Ftracer%2Fstyle.blade.php&line=1", "ajax": false, "filename": "style.blade.php", "line": "?"}}, {"name": "paypal::checkout.onepage.paypal-smart-button", "param_count": null, "params": [], "start": **********.052498, "type": "blade", "hash": "bladeD:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Paypal\\src/resources/views/checkout/onepage/paypal-smart-button.blade.phppaypal::checkout.onepage.paypal-smart-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FPaypal%2Fsrc%2FResources%2Fviews%2Fcheckout%2Fonepage%2Fpaypal-smart-button.blade.php&line=1", "ajax": false, "filename": "paypal-smart-button.blade.php", "line": "?"}}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.03078, "accumulated_duration_str": "30.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `channels` where `hostname` in ('127.0.0.1:8000', 'http://127.0.0.1:8000', 'https://127.0.0.1:8000')", "type": "query", "params": [], "bindings": ["127.0.0.1:8000", "http://127.0.0.1:8000", "https://127.0.0.1:8000"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": "middleware", "name": "theme", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 19}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.802092, "duration": 0.026359999999999998, "duration_str": "26.36ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "geniusdb", "explain": null, "start_percent": 0, "width_percent": 85.64}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "installer_locale", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "theme", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Theme.php", "line": 32}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.837263, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 85.64, "width_percent": 2.307}, {"sql": "select `currencies`.*, `channel_currencies`.`channel_id` as `pivot_channel_id`, `channel_currencies`.`currency_id` as `pivot_currency_id` from `currencies` inner join `channel_currencies` on `currencies`.`id` = `channel_currencies`.`currency_id` where `channel_currencies`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 25}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 23, "namespace": "middleware", "name": "installer_locale", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Locale.php", "line": 40}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.841326, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "geniusdb", "explain": null, "start_percent": 87.947, "width_percent": 3.996}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.8525991, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 91.943, "width_percent": 2.144}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.854413, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 94.087, "width_percent": 1.559}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'geniusdb' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.85604, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "geniusdb", "explain": null, "start_percent": 95.647, "width_percent": 1.949}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "currency", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Shop\\src\\Http\\Middleware\\Currency.php", "line": 40}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.858221, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "geniusdb", "explain": null, "start_percent": 97.596, "width_percent": 2.404}]}, "models": {"data": {"Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/customer/login", "action_name": "shop.customer.session.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\Customer\\SessionController@index", "uri": "GET customer/login", "controller": "Webkul\\Shop\\Http\\Controllers\\Customer\\SessionController@index<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FCustomer%2FSessionController.php&line=28\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "customer/login", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FShop%2Fsrc%2FHttp%2FControllers%2FCustomer%2FSessionController.php&line=28\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Shop/src/Http/Controllers/Customer/SessionController.php:28-35</a>", "middleware": "web, shop, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance", "duration": "901ms", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1071515460 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1071515460\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1327630288 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1327630288\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1832504658 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/customer/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImpPY1BtYUl1WlhrUnQ0KzlZSDdmeFE9PSIsInZhbHVlIjoiVUF3V2lrUFdDbGxrZ1NQVmxVRDRKT3RZN3RlRURqckRwZnhVcndUNEJrNGhYTUhHMCtSY1FUMWppaUVkMkNqalNNUWFzeEdhM0JpczJrUzBETnptT3B3V0xaVUdRa3djWElDR0t0NUlSY09nWDI5UWxKZ3FhNklIZUtjZ24wa3QiLCJtYWMiOiIyZWZiODUyMzEzMTdjNDE4NjBlZDkzNTdkNjU1YjY5N2U4N2RlOWI4MjY5NjA4ZTA4M2Y0MDQ0NTVjYTZhODhjIiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6InhvcTVyR2ZSOXQxYVlUK2ZnMkFLNEE9PSIsInZhbHVlIjoiQkExZHB5V1JSd2ZzdzlhUDZIZEZxSHhJL3JkQkVSdlhIMkVneEQ3NXlFelE1MVRWbmRWVGtabVc3eGkxRWQ3WEpIVjIxRDMwRlVTL0JKOHo2dTdFUlZVeDBWVUFKVDlQWHRVeHNjcVFhVytmdytWUWZDbi93amliekp4d0MvK1MiLCJtYWMiOiIyZjFiMTg4YjVjNTcwNGJlZjFhOTRlZDg0ZjVjMGQzNmE2YWVmOGUyYzE4MDc3ODgzOGFjOTkxYWZjZjhlNDViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1832504658\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1047454006 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">22PabLRJWo5JDWyXjvl1f7ysPbnJSnAbV3HPNor9</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zyKdZhO4anpQ96kSv4L6kxQSM4RPtdpAFrYMdqnu</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047454006\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1514635189 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 14 Jul 2025 12:45:26 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514635189\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2016007708 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">22PabLRJWo5JDWyXjvl1f7ysPbnJSnAbV3HPNor9</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">INR</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/customer/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01K04FFYQXVSDKC636X2GTQ8PY</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"73 characters\">This email is registered as a vendor. Please use the Vendor Login option.</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2016007708\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/customer/login", "action_name": "shop.customer.session.index", "controller_action": "Webkul\\Shop\\Http\\Controllers\\Customer\\SessionController@index"}, "badge": null}}