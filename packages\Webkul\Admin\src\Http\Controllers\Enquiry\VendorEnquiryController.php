<?php

namespace Webkul\Admin\Http\Controllers\Enquiry;

use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Admin\DataGrids\Enquiry\EnquiryDataGrid;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VendorEnquiryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Ensure only vendors can access this controller
        $this->middleware('auth:vendor');
    }

    /**
     * Display a listing of vendor's enquiries.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        if (request()->ajax()) {
            return datagrid(EnquiryDataGrid::class)->process();
        }

        return view('admin::enquiries.vendor.index');
    }

    /**
     * Display the specified enquiry.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $currentVendor = auth()->guard('vendor')->user();

        // Get the enquiry and ensure it belongs to the current vendor's products
        $enquiry = DB::table('enquiries')
            ->leftJoin('customers', 'enquiries.user', '=', 'customers.id')
            ->leftJoin('products', 'enquiries.product', '=', 'products.id')
            ->leftJoin('admins', 'products.vendor', '=', 'admins.id')
            ->select(
                'enquiries.*',
                'customers.first_name',
                'customers.last_name',
                'customers.email as customer_email',
                'products.name as product_name',
                'products.sku as product_sku',
                'products.id as product_id',
                'admins.name as vendor_name'
            )
            ->where('enquiries.id', $id)
            ->where('products.vendor', $currentVendor->id)
            ->first();

        if (!$enquiry) {
            abort(404, 'Enquiry not found or you do not have permission to view it.');
        }

        return view('admin::enquiries.vendor.show', compact('enquiry'));
    }

    /**
     * Update the status of the specified enquiry.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|integer|in:0,1,2', // 0=pending, 1=in-progress, 2=closed
        ]);

        $currentVendor = auth()->guard('vendor')->user();

        // Verify the enquiry belongs to the current vendor's products
        $enquiry = DB::table('enquiries')
            ->leftJoin('products', 'enquiries.product', '=', 'products.id')
            ->where('enquiries.id', $id)
            ->where('products.vendor', $currentVendor->id)
            ->first();

        if (!$enquiry) {
            return response()->json([
                'status' => false,
                'message' => 'Enquiry not found or you do not have permission to update it.'
            ], 404);
        }

        // Update the enquiry status
        DB::table('enquiries')
            ->where('id', $id)
            ->update([
                'status' => $request->status,
                'updated_at' => now(),
            ]);

        $statusText = match($request->status) {
            0 => 'Pending',
            1 => 'In Progress',
            2 => 'Closed',
            default => 'Unknown'
        };

        return response()->json([
            'status' => true,
            'message' => "Enquiry status updated to {$statusText} successfully."
        ]);
    }

    /**
     * Get enquiry statistics for the current vendor.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getStats()
    {
        $currentVendor = auth()->guard('vendor')->user();

        $stats = DB::table('enquiries')
            ->leftJoin('products', 'enquiries.product', '=', 'products.id')
            ->where('products.vendor', $currentVendor->id)
            ->selectRaw('
                COUNT(*) as total,
                SUM(CASE WHEN enquiries.status = 0 THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN enquiries.status = 1 THEN 1 ELSE 0 END) as in_progress,
                SUM(CASE WHEN enquiries.status = 2 THEN 1 ELSE 0 END) as closed
            ')
            ->first();

        return response()->json([
            'status' => true,
            'data' => $stats
        ]);
    }
}
