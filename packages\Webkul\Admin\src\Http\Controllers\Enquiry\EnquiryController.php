<?php

namespace Webkul\Admin\Http\Controllers\Enquiry;

use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Admin\DataGrids\Enquiry\EnquiryDataGrid;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
class EnquiryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
    ) {}

    /**
     * Fetch address by customer id.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        //$customer = $this->customerRepository->find($id);

        return view('admin::enquiries.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        DB::table('enquiries')->insert([
            'user' => auth()->guard('customer')->user()->id,
            'product' => $request->product,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Your enquiry has been submitted successfully.'
        ]);

        // dd('rquest data', $request->all());
        //return view('admin::enquiries.create');
    }

    /**
     * Store a newly created resource in storage.
     */


     public function list()
     {
         return datagrid(EnquiryDataGrid::class)->process();
     }
}
