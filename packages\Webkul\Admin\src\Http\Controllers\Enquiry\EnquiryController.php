<?php

namespace Webkul\Admin\Http\Controllers\Enquiry;

use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Admin\DataGrids\Enquiry\EnquiryDataGrid;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
class EnquiryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
    ) {}

    /**
     * Fetch address by customer id.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        //$customer = $this->customerRepository->find($id);

        return view('admin::enquiries.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        DB::table('enquiries')->insert([
            'user' => auth()->guard('customer')->user()->id,
            'product' => $request->product,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Your enquiry has been submitted successfully.'
        ]);

        // dd('rquest data', $request->all());
        //return view('admin::enquiries.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        // This method can be implemented later if needed
        return response()->json([
            'status' => false,
            'message' => 'Store method not implemented yet.'
        ], 501);
    }

    /**
     * Display the specified enquiry.
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // This method can be implemented later if needed
        return response()->json([
            'status' => false,
            'message' => 'Show method not implemented yet.'
        ], 501);
    }

    /**
     * Update the specified enquiry.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        // This method can be implemented later if needed
        return response()->json([
            'status' => false,
            'message' => 'Update method not implemented yet.'
        ], 501);
    }

    /**
     * Remove the specified enquiry.
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        // This method can be implemented later if needed
        return response()->json([
            'status' => false,
            'message' => 'Destroy method not implemented yet.'
        ], 501);
    }

     public function list()
     {
         try {
             return datagrid(EnquiryDataGrid::class)->process();
         } catch (\Exception $e) {
             \Log::error('Enquiry DataGrid Error: ' . $e->getMessage());
             \Log::error('Stack trace: ' . $e->getTraceAsString());

             return response()->json([
                 'status' => false,
                 'message' => 'Error loading enquiries: ' . $e->getMessage()
             ], 500);
         }
     }

    /**
     * Mass delete enquiries.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function massDelete(Request $request)
    {
        $request->validate([
            'indices' => 'required|array',
            'indices.*' => 'integer',
        ]);

        try {
            // Ensure indices is an array and contains valid integers
            $indices = $request->input('indices', []);

            if (empty($indices) || !is_array($indices)) {
                return response()->json([
                    'status' => false,
                    'message' => 'No valid enquiries selected for deletion.'
                ], 400);
            }

            // Filter out any non-numeric values
            $validIndices = array_filter($indices, function($id) {
                return is_numeric($id) && $id > 0;
            });

            if (empty($validIndices)) {
                return response()->json([
                    'status' => false,
                    'message' => 'No valid enquiries selected for deletion.'
                ], 400);
            }

            $deletedCount = DB::table('enquiries')
                ->whereIn('id', $validIndices)
                ->delete();

            return response()->json([
                'status' => true,
                'message' => trans('admin::app.enquiries.index.mass-delete-success', ['count' => $deletedCount])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error deleting enquiries: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mass update enquiry status.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function massUpdateStatus(Request $request)
    {
        $request->validate([
            'indices' => 'required|array',
            'indices.*' => 'integer',
            'value' => 'required|integer|in:0,1,2', // 0=pending, 1=in-progress, 2=closed
        ]);

        try {
            // Ensure indices is an array and contains valid integers
            $indices = $request->input('indices', []);
            $statusValue = $request->input('value');

            if (empty($indices) || !is_array($indices)) {
                return response()->json([
                    'status' => false,
                    'message' => 'No valid enquiries selected for update.'
                ], 400);
            }

            // Filter out any non-numeric values
            $validIndices = array_filter($indices, function($id) {
                return is_numeric($id) && $id > 0;
            });

            if (empty($validIndices)) {
                return response()->json([
                    'status' => false,
                    'message' => 'No valid enquiries selected for update.'
                ], 400);
            }

            $updatedCount = DB::table('enquiries')
                ->whereIn('id', $validIndices)
                ->update([
                    'status' => $statusValue,
                    'updated_at' => now(),
                ]);

            $statusText = match($statusValue) {
                0 => 'Pending',
                1 => 'In Progress',
                2 => 'Closed',
                default => 'Unknown'
            };

            return response()->json([
                'status' => true,
                'message' => trans('admin::app.enquiries.index.mass-update-success', [
                    'count' => $updatedCount,
                    'status' => $statusText
                ])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error updating enquiry status: ' . $e->getMessage()
            ], 500);
        }
    }
}
