<?php

namespace Webkul\Admin\Http\Controllers\Enquiry;

use Webkul\Admin\Http\Controllers\Controller;
use Webkul\Admin\DataGrids\Enquiry\EnquiryDataGrid;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
class EnquiryController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
    ) {}

    /**
     * Fetch address by customer id.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        //$customer = $this->customerRepository->find($id);

        return view('admin::enquiries.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create(Request $request)
    {
        DB::table('enquiries')->insert([
            'user' => auth()->guard('customer')->user()->id,
            'product' => $request->product,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json([
            'status' => true,
            'message' => 'Your enquiry has been submitted successfully.'
        ]);

        // dd('rquest data', $request->all());
        //return view('admin::enquiries.create');
    }

    /**
     * Store a newly created resource in storage.
     */


     public function list()
     {
         return datagrid(EnquiryDataGrid::class)->process();
     }

    /**
     * Mass delete enquiries.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function massDelete(Request $request)
    {
        $request->validate([
            'indices' => 'required|array',
            'indices.*' => 'integer',
        ]);

        try {
            $deletedCount = DB::table('enquiries')
                ->whereIn('id', $request->indices)
                ->delete();

            return response()->json([
                'status' => true,
                'message' => trans('admin::app.enquiries.index.mass-delete-success', ['count' => $deletedCount])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error deleting enquiries: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Mass update enquiry status.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function massUpdateStatus(Request $request)
    {
        $request->validate([
            'indices' => 'required|array',
            'indices.*' => 'integer',
            'value' => 'required|integer|in:0,1,2', // 0=pending, 1=in-progress, 2=closed
        ]);

        try {
            $updatedCount = DB::table('enquiries')
                ->whereIn('id', $request->indices)
                ->update([
                    'status' => $request->value,
                    'updated_at' => now(),
                ]);

            $statusText = match($request->value) {
                0 => 'Pending',
                1 => 'In Progress',
                2 => 'Closed',
                default => 'Unknown'
            };

            return response()->json([
                'status' => true,
                'message' => trans('admin::app.enquiries.index.mass-update-success', [
                    'count' => $updatedCount,
                    'status' => $statusText
                ])
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Error updating enquiry status: ' . $e->getMessage()
            ], 500);
        }
    }
}
