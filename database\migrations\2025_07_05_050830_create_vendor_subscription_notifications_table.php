<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vendor_subscription_notifications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vendor_id');
            $table->unsignedBigInteger('subscription_id');
            $table->string('notification_type', 50); // 'expiration_warning', 'expired'
            $table->integer('days_before_expiry');
            $table->timestamp('sent_at');
            $table->timestamps();

            // Indexes for performance
            // $table->index(['vendor_id', 'subscription_id']);
            // $table->index(['notification_type', 'days_before_expiry']);
            // $table->index('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vendor_subscription_notifications');
    }
};
