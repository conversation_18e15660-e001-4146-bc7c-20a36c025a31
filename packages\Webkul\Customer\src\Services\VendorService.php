<?php

namespace Webkul\Customer\Services;

use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Webkul\Customer\Repositories\VendorRepository;
use Webkul\Customer\Mail\Vendor\ApplicationNotification;
use Webkul\Customer\Mail\Vendor\ApprovalNotification;
use Webkul\Customer\Mail\Vendor\RejectionNotification;

class VendorService
{
    /**
     * Create a new service instance.
     *
     * @param  \Webkul\Customer\Repositories\VendorRepository  $vendorRepository
     */
    public function __construct(
        protected VendorRepository $vendorRepository
    ) {}

    /**
     * Register a new vendor.
     *
     * @param  array  $data
     * @return \Webkul\Customer\Contracts\Vendor
     */
    public function register(array $data)
    {
        $vendor = $this->vendorRepository->create($data);

        // Send notification to admin about new vendor application
        $this->notifyAdminOfNewApplication($vendor);

        return $vendor;
    }

    /**
     * Approve a vendor.
     *
     * @param  int  $vendorId
     * @param  int  $adminId
     * @return \Webkul\Customer\Contracts\Vendor
     */
    public function approve($vendorId, $adminId = null)
    {
        // Get vendor role ID
        $vendorRole = DB::table('roles')->where('name', 'Vendor')->first();

        $vendor = $this->vendorRepository->approve($vendorId, $adminId);

        // Assign vendor role
        if ($vendorRole) {
            $this->vendorRepository->update(['role_id' => $vendorRole->id], $vendorId);
            $vendor = $this->vendorRepository->find($vendorId);
        }

        // Send approval notification to vendor
        $this->notifyVendorOfApproval($vendor);

        return $vendor;
    }

    /**
     * Reject a vendor.
     *
     * @param  int  $vendorId
     * @param  string  $reason
     * @param  int  $adminId
     * @return \Webkul\Customer\Contracts\Vendor
     */
    public function reject($vendorId, $reason = null, $adminId = null)
    {
        $vendor = $this->vendorRepository->reject($vendorId, $reason, $adminId);

        // Send rejection notification to vendor
        $this->notifyVendorOfRejection($vendor);

        return $vendor;
    }

    /**
     * Verify a vendor.
     *
     * @param  int  $vendorId
     * @return \Webkul\Customer\Contracts\Vendor
     */
    public function verify($vendorId)
    {
        $vendor = $this->vendorRepository->find($vendorId);

        if ($vendor) {
            $this->vendorRepository->update([
                'is_verified' => true,
                'token'       => null,
            ], $vendorId);

            $vendor = $this->vendorRepository->find($vendorId);
        }

        return $vendor;
    }

    /**
     * Check if vendor can login.
     *
     * @param  string  $email
     * @param  string  $password
     * @return array
     */
    public function canLogin($email, $password)
    {
        $vendor = $this->vendorRepository->findByEmail($email);

        if (! $vendor) {
            return [
                'success' => false,
                'message' => 'Invalid credentials.',
                'vendor'  => null,
            ];
        }

        if (! \Hash::check($password, $vendor->password)) {
            return [
                'success' => false,
                'message' => 'Invalid credentials.',
                'vendor'  => $vendor,
            ];
        }

        if (! $vendor->isApproved()) {
            $message = $vendor->isPending()
                ? 'Your vendor application is under review. Please wait for admin approval.'
                : 'Your vendor application has been rejected. Please contact support.';

            return [
                'success' => false,
                'message' => $message,
                'vendor'  => $vendor,
            ];
        }

        if (! $vendor->is_verified) {
            return [
                'success' => false,
                'message' => 'Please verify your email address before logging in.',
                'vendor'  => $vendor,
            ];
        }

        return [
            'success' => true,
            'vendor'  => $vendor,
        ];
    }

    /**
     * Send notification to admin about new vendor application.
     *
     * @param  \Webkul\Customer\Contracts\Vendor  $vendor
     * @return void
     */
    protected function notifyAdminOfNewApplication($vendor)
    {
        try {
            if (core()->getConfigData('emails.general.notifications.emails.general.notifications.vendor_application_notification_to_admin')) {
                Mail::queue(new ApplicationNotification($vendor));
            }
        } catch (\Exception $e) {
            report($e);
        }
    }

    /**
     * Send approval notification to vendor.
     *
     * @param  \Webkul\Customer\Contracts\Vendor  $vendor
     * @return void
     */
    protected function notifyVendorOfApproval($vendor)
    {
        try {
            if (core()->getConfigData('emails.general.notifications.emails.general.notifications.vendor_approval_notification')) {
                Mail::queue(new ApprovalNotification($vendor));
            }
        } catch (\Exception $e) {
            report($e);
        }
    }

    /**
     * Send rejection notification to vendor.
     *
     * @param  \Webkul\Customer\Contracts\Vendor  $vendor
     * @return void
     */
    protected function notifyVendorOfRejection($vendor)
    {
        try {
            if (core()->getConfigData('emails.general.notifications.emails.general.notifications.vendor_rejection_notification')) {
                Mail::queue(new RejectionNotification($vendor));
            }
        } catch (\Exception $e) {
            report($e);
        }
    }

    /**
     * Check if email belongs to a vendor.
     *
     * @param  string  $email
     * @return \Webkul\Customer\Models\Vendor|null
     */
    public function findByEmail($email)
    {
        return $this->vendorRepository->findByEmail($email);
    }
}
