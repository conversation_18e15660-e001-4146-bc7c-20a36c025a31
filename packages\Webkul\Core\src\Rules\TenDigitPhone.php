<?php

namespace Webkul\Core\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class TenDigitPhone implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        /**
         * Simple 10-digit phone number validation:
         * 1. Must be exactly 10 digits
         * 2. No alphabets allowed
         * 3. No special characters except spaces (which will be removed)
         * 4. No country codes
         */
        
        // Remove any spaces for validation
        $cleanValue = str_replace(' ', '', $value);
        
        // Check if it contains only digits
        if (!ctype_digit($cleanValue)) {
            $fail('The :attribute must contain only numbers.');
            return;
        }
        
        // Check if it's exactly 10 digits
        if (strlen($cleanValue) !== 10) {
            $fail('The :attribute must be exactly 10 digits.');
            return;
        }
        
        // Additional check: ensure it doesn't start with 0 (Indian mobile numbers don't start with 0)
        if (substr($cleanValue, 0, 1) === '0') {
            $fail('The :attribute cannot start with 0.');
            return;
        }
    }
}
