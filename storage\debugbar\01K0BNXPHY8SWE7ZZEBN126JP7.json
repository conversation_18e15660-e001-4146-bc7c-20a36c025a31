{"__meta": {"id": "01K0BNXPHY8SWE7ZZEBN126JP7", "datetime": "2025-07-17 13:22:28", "utime": **********.99204, "method": "GET", "uri": "/cache/logo/bagisto.png", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 1, "messages": [{"message": "[13:22:28] LOG.warning: Creation of dynamic property Intervention\\Image\\Image::$cachekey is deprecated in D:\\venkat clone repo\\geniusmart\\mart\\digitalmartgenius\\vendor\\bagisto\\image-cache\\src\\Intervention\\Image\\ImageCache.php on line 275", "message_html": null, "is_string": false, "label": "warning", "time": **********.971912, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752738745.349764, "end": **********.022128, "duration": 3.6723639965057373, "duration_str": "3.67s", "measures": [{"label": "Booting", "start": 1752738745.349764, "relative_start": 0, "end": **********.370135, "relative_end": **********.370135, "duration": 1.****************, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.370165, "relative_start": 1.****************, "end": **********.022131, "relative_end": 2.86102294921875e-06, "duration": 2.***************, "duration_str": "2.65s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.414512, "relative_start": 1.****************, "end": **********.428781, "relative_end": **********.428781, "duration": 0.014269113540649414, "duration_str": "14.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.985414, "relative_start": 3.****************, "end": **********.985868, "relative_end": **********.985868, "duration": 0.000453948974609375, "duration_str": "454μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.985903, "relative_start": 3.***************, "end": **********.985942, "relative_end": **********.985942, "duration": 3.886222839355469e-05, "duration_str": "39μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "32MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.12", "Environment": "local", "Debug Mode": "Enabled", "URL": "127.0.0.1:8000", "Timezone": "Asia/Kolkata", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse", "uri": "GET cache/{template}/{filename}", "controller": "Webkul\\Core\\ImageCache\\Controller@getResponse<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Fvenkat%20clone%20repo%2Fgeniusmart%2Fmart%2Fdigitalmartgenius%2Fpackages%2FWebkul%2FCore%2Fsrc%2FImageCache%2FController.php&line=34\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Core/src/ImageCache/Controller.php:34-46</a>", "duration": "3.67s", "peak_memory": "34MB", "response": "image/png", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-606131381 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-606131381\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2113665396 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2113665396\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1736590599 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Brave&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6InFNTm0veEpmRDNzYjBJMmZTZTRzVlE9PSIsInZhbHVlIjoiVkx1Z1p2OFRXeHBkU1ZjbHFnaE1mOFkvR0YvMjZPTTRtcGZBencyL0l2UVZMZlcxRG1ScFlpbjM5L251eXNYenJxUlZLK1FVZ0ZjNnMyMHJjS2lpV3htUDRjcjV4ckRQejZYanJBbUZGSmNDVndXUWJzM3NTbzhlT0tNTXhUOHMiLCJtYWMiOiIwYWIyZjYzYmM2ODIyMzBmNTA5NzNhMTcyYjFhMjFmNDg1NTc4MjU4MjM5NTY0M2JhMTY1NzFlZTI4MTM1N2I1IiwidGFnIjoiIn0%3D; bagisto_session=eyJpdiI6IitETVIwM05rbkNUamdCVlAveVFGb3c9PSIsInZhbHVlIjoiSXZRcHNZYm45VEVxcTRTS1gybnlJRVdqU2VLdSttZHk4N3lIL3hhdURZSHUySGVLRVZGYTd5VkhOQmZiNGFjV0h2a25CYmJGTGRLcnZlY1lMeFZ4WGdXMmFsQ2dFaUdHRkwzOFNpQU52WGE3OTJjOFNnTVN6OFBad001eXBLdG8iLCJtYWMiOiIzZGExNGFmNjA5NTlmMGM2OWYyZjFmMDQ0ZWM1OWJmYmM2MzU3MTNiOGMxZGNhNjZkYmViMTUyYjExNTVjZmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1736590599\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-203745857 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InFNTm0veEpmRDNzYjBJMmZTZTRzVlE9PSIsInZhbHVlIjoiVkx1Z1p2OFRXeHBkU1ZjbHFnaE1mOFkvR0YvMjZPTTRtcGZBencyL0l2UVZMZlcxRG1ScFlpbjM5L251eXNYenJxUlZLK1FVZ0ZjNnMyMHJjS2lpV3htUDRjcjV4ckRQejZYanJBbUZGSmNDVndXUWJzM3NTbzhlT0tNTXhUOHMiLCJtYWMiOiIwYWIyZjYzYmM2ODIyMzBmNTA5NzNhMTcyYjFhMjFmNDg1NTc4MjU4MjM5NTY0M2JhMTY1NzFlZTI4MTM1N2I1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>bagisto_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IitETVIwM05rbkNUamdCVlAveVFGb3c9PSIsInZhbHVlIjoiSXZRcHNZYm45VEVxcTRTS1gybnlJRVdqU2VLdSttZHk4N3lIL3hhdURZSHUySGVLRVZGYTd5VkhOQmZiNGFjV0h2a25CYmJGTGRLcnZlY1lMeFZ4WGdXMmFsQ2dFaUdHRkwzOFNpQU52WGE3OTJjOFNnTVN6OFBad001eXBLdG8iLCJtYWMiOiIzZGExNGFmNjA5NTlmMGM2OWYyZjFmMDQ0ZWM1OWJmYmM2MzU3MTNiOGMxZGNhNjZkYmViMTUyYjExNTVjZmU4IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203745857\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1590846465 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">image/png</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">max-age=604800, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">388</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>etag</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">d4539dad43ec62363cf4fe3d98a2b893</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 07:52:28 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590846465\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1718677759 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1718677759\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/cache/logo/bagisto.png", "action_name": "imagecache", "controller_action": "Webkul\\Core\\ImageCache\\Controller@getResponse"}, "badge": null}}