<!-- SEO Meta Content -->
<?php $__env->startPush('meta'); ?>
    <meta name="description" content="<?php echo app('translator')->get('shop::app.customers.login-form.page-title'); ?>"/>

    <meta name="keywords" content="<?php echo app('translator')->get('shop::app.customers.login-form.page-title'); ?>"/>
<?php $__env->stopPush(); ?>

<?php if (isset($component)) { $__componentOriginal2643b7d197f48caff2f606750db81304 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2643b7d197f48caff2f606750db81304 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.layouts.index','data' => ['hasHeader' => false,'hasFeature' => false,'hasFooter' => false]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::layouts'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['has-header' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'has-feature' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false),'has-footer' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(false)]); ?>
    <!-- Page Title -->
     <?php $__env->slot('title', null, []); ?> 
        <?php echo app('translator')->get('shop::app.customers.login-form.page-title'); ?>
     <?php $__env->endSlot(); ?>

    <div class="container mt-20 max-1180:px-5 max-md:mt-12">
        <?php echo view_render_event('bagisto.shop.customers.login.logo.before'); ?>


        <!-- Company Logo -->
        <div class="flex items-center gap-x-14 max-[1180px]:gap-x-9">
            <a
                href="<?php echo e(route('shop.home.index')); ?>"
                class="m-[0_auto_20px_auto]"
                aria-label="<?php echo app('translator')->get('shop::app.customers.login-form.bagisto'); ?>"
            >
                <img
                    src="<?php echo e(core()->getCurrentChannel()->logo_url ?? bagisto_asset('images/logo.svg')); ?>"
                    alt="<?php echo e(config('app.name')); ?>"
                    width="131"
                    height="29"
                >
            </a>
        </div>

        <?php echo view_render_event('bagisto.shop.customers.login.logo.after'); ?>


        <!-- Form Container -->
        <div class="m-auto w-full max-w-[870px] rounded-xl border border-zinc-200 p-16 px-[90px] max-md:px-8 max-md:py-8 max-sm:border-none max-sm:p-0">
            <h1 class="font-dmserif text-4xl max-md:text-3xl max-sm:text-xl">
                <?php echo app('translator')->get('shop::app.customers.login-form.page-title'); ?>
            </h1>

            <p class="mt-4 text-xl text-zinc-500 max-sm:mt-0 max-sm:text-sm">
                <?php echo app('translator')->get('shop::app.customers.login-form.form-login-text'); ?>
            </p>

            <?php echo view_render_event('bagisto.shop.customers.login.before'); ?>


            <div class="mt-14 rounded max-sm:mt-8">
                <!-- Login Type Selection -->
                <div class="mb-8">
                    <div class="flex gap-4 max-sm:flex-col">
                        <!-- Customer Login -->
                        <div class="flex-1">
                            <input
                                type="radio"
                                id="customer-login"
                                name="login_type"
                                value="customer"
                                class="peer hidden"
                                checked
                                onchange="switchLoginType('customer')"
                            />
                            <label
                                for="customer-login"
                                class="login-type-label active flex cursor-pointer items-center justify-center rounded-xl border-2 border-navyBlue bg-navyBlue text-white p-6 text-center shadow-lg max-sm:p-4"
                            >
                                <div>
                                    <div class="mb-2 flex justify-center">
                                        <span class="icon-customer login-type-icon text-2xl max-sm:text-xl"></span>
                                    </div>
                                    <div class="text-lg font-semibold max-sm:text-base">Customer Login</div>
                                    <div class="text-sm opacity-75 max-sm:text-xs">Shop and manage your orders</div>
                                </div>
                            </label>
                        </div>

                        <!-- Vendor Login -->
                        <div class="flex-1">
                            <input
                                type="radio"
                                id="vendor-login"
                                name="login_type"
                                value="vendor"
                                class="peer hidden"
                                onchange="switchLoginType('vendor')"
                            />
                            <label
                                for="vendor-login"
                                class="login-type-label inactive flex cursor-pointer items-center justify-center rounded-xl border-2 border-zinc-200 bg-white text-gray-700 p-6 text-center max-sm:p-4"
                            >
                                <div>
                                    <div class="mb-2 flex justify-center">
                                        <span class="icon-store login-type-icon text-2xl max-sm:text-xl"></span>
                                    </div>
                                    <div class="text-lg font-semibold max-sm:text-base">Vendor Login</div>
                                    <div class="text-sm opacity-75 max-sm:text-xs">Manage your business and products</div>
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <?php if (isset($component)) { $__componentOriginal4d3fcee3e355fb6c8889181b04f357cc = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal4d3fcee3e355fb6c8889181b04f357cc = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.index','data' => ['id' => 'login-form','action' => route('shop.customer.session.create')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'login-form','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('shop.customer.session.create'))]); ?>
                    <!-- Hidden field to track login type -->
                    <input type="hidden" name="login_type" id="login_type_field" value="customer" />

                    <?php echo view_render_event('bagisto.shop.customers.login_form_controls.before'); ?>


                    <!-- Email -->
                    <?php if (isset($component)) { $__componentOriginal578beb4bb944f523450535628cf00b41 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal578beb4bb944f523450535628cf00b41 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <?php if (isset($component)) { $__componentOriginal2f2718777649517fc23f75e819ccd670 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2f2718777649517fc23f75e819ccd670 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.label','data' => ['class' => 'required text-base font-medium text-gray-800 max-sm:text-sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group.label'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'required text-base font-medium text-gray-800 max-sm:text-sm']); ?>
                            <?php echo app('translator')->get('shop::app.customers.login-form.email'); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2f2718777649517fc23f75e819ccd670)): ?>
<?php $attributes = $__attributesOriginal2f2718777649517fc23f75e819ccd670; ?>
<?php unset($__attributesOriginal2f2718777649517fc23f75e819ccd670); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2f2718777649517fc23f75e819ccd670)): ?>
<?php $component = $__componentOriginal2f2718777649517fc23f75e819ccd670; ?>
<?php unset($__componentOriginal2f2718777649517fc23f75e819ccd670); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal03b54b937596232babb8a12a8847d013 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal03b54b937596232babb8a12a8847d013 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.control','data' => ['type' => 'email','class' => 'w-full rounded-xl border border-zinc-300 px-6 py-4 text-base transition-all duration-300 focus:border-navyBlue focus:ring-2 focus:ring-navyBlue focus:ring-opacity-20 max-md:py-3 max-sm:py-2 max-sm:text-sm','name' => 'email','rules' => 'required|email','value' => '','label' => trans('shop::app.customers.login-form.email'),'placeholder' => '<EMAIL>','ariaLabel' => trans('shop::app.customers.login-form.email'),'ariaRequired' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group.control'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'email','class' => 'w-full rounded-xl border border-zinc-300 px-6 py-4 text-base transition-all duration-300 focus:border-navyBlue focus:ring-2 focus:ring-navyBlue focus:ring-opacity-20 max-md:py-3 max-sm:py-2 max-sm:text-sm','name' => 'email','rules' => 'required|email','value' => '','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('shop::app.customers.login-form.email')),'placeholder' => '<EMAIL>','aria-label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('shop::app.customers.login-form.email')),'aria-required' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal03b54b937596232babb8a12a8847d013)): ?>
<?php $attributes = $__attributesOriginal03b54b937596232babb8a12a8847d013; ?>
<?php unset($__attributesOriginal03b54b937596232babb8a12a8847d013); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal03b54b937596232babb8a12a8847d013)): ?>
<?php $component = $__componentOriginal03b54b937596232babb8a12a8847d013; ?>
<?php unset($__componentOriginal03b54b937596232babb8a12a8847d013); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.error','data' => ['controlName' => 'email']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['control-name' => 'email']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf)): ?>
<?php $attributes = $__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf; ?>
<?php unset($__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf)): ?>
<?php $component = $__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf; ?>
<?php unset($__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf); ?>
<?php endif; ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal578beb4bb944f523450535628cf00b41)): ?>
<?php $attributes = $__attributesOriginal578beb4bb944f523450535628cf00b41; ?>
<?php unset($__attributesOriginal578beb4bb944f523450535628cf00b41); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal578beb4bb944f523450535628cf00b41)): ?>
<?php $component = $__componentOriginal578beb4bb944f523450535628cf00b41; ?>
<?php unset($__componentOriginal578beb4bb944f523450535628cf00b41); ?>
<?php endif; ?>

                    <!-- Password -->
                    <?php if (isset($component)) { $__componentOriginal578beb4bb944f523450535628cf00b41 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal578beb4bb944f523450535628cf00b41 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
                        <?php if (isset($component)) { $__componentOriginal2f2718777649517fc23f75e819ccd670 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2f2718777649517fc23f75e819ccd670 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.label','data' => ['class' => 'required text-base font-medium text-gray-800 max-sm:text-sm']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group.label'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'required text-base font-medium text-gray-800 max-sm:text-sm']); ?>
                            <?php echo app('translator')->get('shop::app.customers.login-form.password'); ?>
                         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2f2718777649517fc23f75e819ccd670)): ?>
<?php $attributes = $__attributesOriginal2f2718777649517fc23f75e819ccd670; ?>
<?php unset($__attributesOriginal2f2718777649517fc23f75e819ccd670); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2f2718777649517fc23f75e819ccd670)): ?>
<?php $component = $__componentOriginal2f2718777649517fc23f75e819ccd670; ?>
<?php unset($__componentOriginal2f2718777649517fc23f75e819ccd670); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal03b54b937596232babb8a12a8847d013 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal03b54b937596232babb8a12a8847d013 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.control','data' => ['type' => 'password','class' => 'w-full rounded-xl border border-zinc-300 px-6 py-4 text-base transition-all duration-300 focus:border-navyBlue focus:ring-2 focus:ring-navyBlue focus:ring-opacity-20 max-md:py-3 max-sm:py-2 max-sm:text-sm','id' => 'password','name' => 'password','rules' => 'required|min:6','value' => '','label' => trans('shop::app.customers.login-form.password'),'placeholder' => trans('shop::app.customers.login-form.password'),'ariaLabel' => trans('shop::app.customers.login-form.password'),'ariaRequired' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group.control'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'password','class' => 'w-full rounded-xl border border-zinc-300 px-6 py-4 text-base transition-all duration-300 focus:border-navyBlue focus:ring-2 focus:ring-navyBlue focus:ring-opacity-20 max-md:py-3 max-sm:py-2 max-sm:text-sm','id' => 'password','name' => 'password','rules' => 'required|min:6','value' => '','label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('shop::app.customers.login-form.password')),'placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('shop::app.customers.login-form.password')),'aria-label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('shop::app.customers.login-form.password')),'aria-required' => 'true']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal03b54b937596232babb8a12a8847d013)): ?>
<?php $attributes = $__attributesOriginal03b54b937596232babb8a12a8847d013; ?>
<?php unset($__attributesOriginal03b54b937596232babb8a12a8847d013); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal03b54b937596232babb8a12a8847d013)): ?>
<?php $component = $__componentOriginal03b54b937596232babb8a12a8847d013; ?>
<?php unset($__componentOriginal03b54b937596232babb8a12a8847d013); ?>
<?php endif; ?>

                        <?php if (isset($component)) { $__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'shop::components.form.control-group.error','data' => ['controlName' => 'password']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('shop::form.control-group.error'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['control-name' => 'password']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf)): ?>
<?php $attributes = $__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf; ?>
<?php unset($__attributesOriginal72f1d7ac608c1db7c92b56fb85299dbf); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf)): ?>
<?php $component = $__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf; ?>
<?php unset($__componentOriginal72f1d7ac608c1db7c92b56fb85299dbf); ?>
<?php endif; ?>
                     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal578beb4bb944f523450535628cf00b41)): ?>
<?php $attributes = $__attributesOriginal578beb4bb944f523450535628cf00b41; ?>
<?php unset($__attributesOriginal578beb4bb944f523450535628cf00b41); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal578beb4bb944f523450535628cf00b41)): ?>
<?php $component = $__componentOriginal578beb4bb944f523450535628cf00b41; ?>
<?php unset($__componentOriginal578beb4bb944f523450535628cf00b41); ?>
<?php endif; ?>

                    <div class="flex justify-between items-center max-sm:flex-col max-sm:gap-4">
                        <div class="flex select-none items-center gap-2">
                            <input
                                type="checkbox"
                                id="show-password"
                                class="peer hidden"
                                onchange="switchVisibility()"
                            />

                            <label
                                class="icon-uncheck peer-checked:icon-check-box cursor-pointer text-2xl text-navyBlue peer-checked:text-navyBlue transition-colors duration-200 max-sm:text-xl"
                                for="show-password"
                            ></label>

                            <label
                                class="cursor-pointer select-none text-base text-zinc-600 hover:text-navyBlue transition-colors duration-200 max-sm:text-sm ltr:pl-0 rtl:pr-0"
                                for="show-password"
                            >
                                <?php echo app('translator')->get('shop::app.customers.login-form.show-password'); ?>
                            </label>
                        </div>

                        <div class="block">
                            <a
                                href="<?php echo e(route('shop.customers.forgot_password.create')); ?>"
                                class="cursor-pointer text-base text-navyBlue hover:text-darkBlue font-medium transition-colors duration-200 max-sm:text-sm"
                            >
                                <span>
                                    <?php echo app('translator')->get('shop::app.customers.login-form.forgot-pass'); ?>
                                </span>
                            </a>
                        </div>
                    </div>

                    <!-- Captcha -->
                    <?php if(core()->getConfigData('customer.captcha.credentials.status')): ?>
                        <div class="mt-5 flex">
                            <?php echo \Webkul\Customer\Facades\Captcha::render(); ?>

                        </div>
                    <?php endif; ?>

                    <!-- Submit Button -->
                    <div class="mt-8 flex flex-wrap items-center gap-9 max-sm:justify-center max-sm:gap-5 max-sm:text-center">
                        <button
                            class="primary-button m-0 mx-auto block w-full max-w-[374px] rounded-2xl px-11 py-4 text-center text-base font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105 max-md:max-w-full max-md:rounded-lg max-md:py-3 max-sm:py-3 ltr:ml-0 rtl:mr-0"
                            type="submit"
                        >
                            <?php echo app('translator')->get('shop::app.customers.login-form.button-title'); ?>
                        </button>

                        <?php echo view_render_event('bagisto.shop.customers.login_form_controls.after'); ?>

                    </div>
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal4d3fcee3e355fb6c8889181b04f357cc)): ?>
<?php $attributes = $__attributesOriginal4d3fcee3e355fb6c8889181b04f357cc; ?>
<?php unset($__attributesOriginal4d3fcee3e355fb6c8889181b04f357cc); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal4d3fcee3e355fb6c8889181b04f357cc)): ?>
<?php $component = $__componentOriginal4d3fcee3e355fb6c8889181b04f357cc; ?>
<?php unset($__componentOriginal4d3fcee3e355fb6c8889181b04f357cc); ?>
<?php endif; ?>
            </div>

            <?php echo view_render_event('bagisto.shop.customers.login.after'); ?>


            <div class="mt-8 text-center">
                <p class="font-medium text-zinc-600 max-sm:text-sm">
                    <?php echo app('translator')->get('shop::app.customers.login-form.new-customer'); ?>
                    <a
                        class="text-navyBlue hover:text-darkBlue font-semibold transition-colors duration-200 underline decoration-2 underline-offset-2"
                        href="<?php echo e(route('shop.customers.register.index')); ?>"
                    >
                        <?php echo app('translator')->get('shop::app.customers.login-form.create-your-account'); ?>
                    </a>
                </p>
            </div>
        </div>

        <p class="mb-4 mt-8 text-center text-xs text-zinc-500">
            <?php echo app('translator')->get('shop::app.customers.login-form.footer', ['current_year'=> date('Y') ]); ?>
        </p>
    </div>

    <?php $__env->startPush('scripts'); ?>
        <?php echo \Webkul\Customer\Facades\Captcha::renderJS(); ?>


        <script>
            function switchVisibility() {
                let passwordField = document.getElementById("password");

                passwordField.type = passwordField.type === "password"
                    ? "text"
                    : "password";
            }

            function switchLoginType(type) {
                const loginForm = document.getElementById('login-form');
                const loginTypeField = document.getElementById('login_type_field');

                loginTypeField.value = type;

                // Get the labels for visual styling
                const customerLabel = document.querySelector('label[for="customer-login"]');
                const vendorLabel = document.querySelector('label[for="vendor-login"]');

                // Reset both labels to inactive state
                customerLabel.classList.remove('active');
                customerLabel.classList.add('inactive');

                vendorLabel.classList.remove('active');
                vendorLabel.classList.add('inactive');

                // Apply active styling to selected type
                if (type === 'vendor') {
                    vendorLabel.classList.remove('inactive');
                    vendorLabel.classList.add('active');
                    loginForm.action = "<?php echo e(route('shop.customer.session.create')); ?>";
                } else {
                    customerLabel.classList.remove('inactive');
                    customerLabel.classList.add('active');
                    loginForm.action = "<?php echo e(route('shop.customer.session.create')); ?>";
                }
            }

            // Initialize login type styling on page load
            document.addEventListener('DOMContentLoaded', function() {
                // Ensure customer login is selected by default
                switchLoginType('customer');

                // Add email validation for customer login
                const emailInput = document.querySelector('input[name="email"]');
                const loginForm = document.getElementById('login-form');

                if (emailInput && loginForm) {
                    // Check email when user leaves the email field
                    emailInput.addEventListener('blur', function() {
                        const loginType = document.getElementById('login_type_field').value;
                        if (loginType === 'customer' && this.value) {
                            checkEmailType(this.value);
                        }
                    });

                    // Also check on form submission
                    loginForm.addEventListener('submit', function(e) {
                        const loginType = document.getElementById('login_type_field').value;
                        const email = emailInput.value;

                        if (loginType === 'customer' && email) {
                            e.preventDefault();
                            checkEmailTypeBeforeSubmit(email, this);
                        }
                    });
                }
            });

            // Function to check email type
            function checkEmailType(email) {
                fetch('<?php echo e(route("shop.check-email-type")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    const emailInput = document.querySelector('input[name="email"]');
                    const errorDiv = document.querySelector('.email-error-message');

                    // Remove existing error message
                    if (errorDiv) {
                        errorDiv.remove();
                    }

                    if (data.is_vendor) {
                        // Show error message
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'email-error-message text-red-600 text-sm mt-2 p-3 bg-red-50 border border-red-200 rounded-lg';
                        errorMessage.innerHTML = `
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                ${data.message}
                            </div>
                        `;
                        emailInput.parentNode.appendChild(errorMessage);
                        emailInput.classList.add('border-red-500');
                    } else {
                        emailInput.classList.remove('border-red-500');
                    }
                })
                .catch(error => {
                    console.error('Error checking email type:', error);
                });
            }

            // Function to check email before form submission
            function checkEmailTypeBeforeSubmit(email, form) {
                fetch('<?php echo e(route("shop.check-email-type")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({ email: email })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.is_vendor) {
                        // Show alert and prevent submission
                        alert(data.message);
                        return false;
                    } else {
                        // Submit the form
                        form.submit();
                    }
                })
                .catch(error => {
                    console.error('Error checking email type:', error);
                    // Allow form submission on error
                    form.submit();
                });
            }
        </script>

        <style>
            /* Enhanced login type switcher styling */
            .login-type-label {
                transition: all 0.3s ease-in-out !important;
                border-width: 2px !important;
            }

            .login-type-label:hover {
                border-color: #060C3B !important;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
                transform: translateY(-1px);
            }

            .login-type-label.active {
                border-color: #060C3B !important;
                background-color: #060C3B !important;
                color: white !important;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important;
                transform: translateY(-2px);
            }

            .login-type-label.inactive {
                border-color: #d1d5db !important;
                background-color: white !important;
                color: #374151 !important;
                box-shadow: none !important;
                transform: translateY(0);
            }

            /* Icon styling */
            .login-type-icon {
                transition: all 0.3s ease-in-out;
            }

            .login-type-label.active .login-type-icon {
                transform: scale(1.1);
            }
        </style>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2643b7d197f48caff2f606750db81304)): ?>
<?php $attributes = $__attributesOriginal2643b7d197f48caff2f606750db81304; ?>
<?php unset($__attributesOriginal2643b7d197f48caff2f606750db81304); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2643b7d197f48caff2f606750db81304)): ?>
<?php $component = $__componentOriginal2643b7d197f48caff2f606750db81304; ?>
<?php unset($__componentOriginal2643b7d197f48caff2f606750db81304); ?>
<?php endif; ?>
<?php /**PATH D:\venkat clone repo\geniusmart\mart\digitalmartgenius\packages\Webkul\Shop\src/resources/views/customers/sign-in.blade.php ENDPATH**/ ?>