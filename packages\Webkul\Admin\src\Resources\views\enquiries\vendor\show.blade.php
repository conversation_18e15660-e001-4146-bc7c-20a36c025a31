<x-admin::layouts>
    <x-slot:title>
        @lang('admin::app.enquiries.vendor.show.title') #{{ $enquiry->id }}
    </x-slot>

    <div class="flex items-center justify-between gap-4 max-sm:flex-wrap">
        <!-- Back Button and Title -->
        <div class="flex items-center gap-4">
            <a
                href="{{ route('admin.enquiries.vendor.index') }}"
                class="grid h-9 w-9 place-content-center rounded-md border border-gray-200 transition-all hover:border-gray-400"
            >
                <span class="icon-arrow-left text-2xl"></span>
            </a>
            
            <p class="text-xl font-bold text-gray-800 dark:text-white">
                @lang('admin::app.enquiries.vendor.show.title') #{{ $enquiry->id }}
            </p>
        </div>

        <!-- Status Update -->
        <div class="flex items-center gap-4">
            <select 
                id="status-select" 
                class="rounded-lg border border-gray-300 px-3 py-2 text-sm"
                onchange="updateStatus(this.value)"
            >
                <option value="0" {{ $enquiry->status == 0 ? 'selected' : '' }}>Pending</option>
                <option value="1" {{ $enquiry->status == 1 ? 'selected' : '' }}>In Progress</option>
                <option value="2" {{ $enquiry->status == 2 ? 'selected' : '' }}>Closed</option>
            </select>
        </div>
    </div>

    <!-- Enquiry Details Card -->
    <div class="mt-8 rounded-xl border border-zinc-200 p-6">
        <div class="grid gap-8 md:grid-cols-2">
            <!-- Customer Information -->
            <div>
                <h3 class="mb-4 text-lg font-semibold text-gray-800">
                    @lang('admin::app.enquiries.vendor.show.customer-info')
                </h3>
                
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-600">Customer Name:</span>
                        <p class="text-base text-gray-800">{{ $enquiry->first_name }} {{ $enquiry->last_name }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">Email:</span>
                        <p class="text-base text-gray-800">
                            <a href="mailto:{{ $enquiry->customer_email }}" class="text-blue-600 hover:text-blue-800">
                                {{ $enquiry->customer_email }}
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- Product Information -->
            <div>
                <h3 class="mb-4 text-lg font-semibold text-gray-800">
                    @lang('admin::app.enquiries.vendor.show.product-info')
                </h3>
                
                <div class="space-y-3">
                    <div>
                        <span class="text-sm font-medium text-gray-600">Product Name:</span>
                        <p class="text-base text-gray-800">{{ $enquiry->product_name }}</p>
                    </div>
                    
                    <div>
                        <span class="text-sm font-medium text-gray-600">SKU:</span>
                        <p class="text-base text-gray-800">{{ $enquiry->product_sku }}</p>
                    </div>
                    
                    <div>
                        <a
                            href="{{ route('admin.catalog.products.edit', $enquiry->product_id) }}"
                            class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800"
                            target="_blank"
                        >
                            <span>View Product</span>
                            <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enquiry Status and Timeline -->
        <div class="mt-8 border-t border-gray-200 pt-6">
            <h3 class="mb-4 text-lg font-semibold text-gray-800">
                @lang('admin::app.enquiries.vendor.show.enquiry-status')
            </h3>
            
            <div class="grid gap-6 md:grid-cols-3">
                <div>
                    <span class="text-sm font-medium text-gray-600">Current Status:</span>
                    <span class="ml-2 inline-block rounded-full px-3 py-1 text-sm font-medium
                        @if($enquiry->status == 0) bg-yellow-100 text-yellow-800
                        @elseif($enquiry->status == 1) bg-blue-100 text-blue-800
                        @elseif($enquiry->status == 2) bg-green-100 text-green-800
                        @else bg-gray-100 text-gray-800
                        @endif
                    ">
                        @if($enquiry->status == 0) Pending
                        @elseif($enquiry->status == 1) In Progress
                        @elseif($enquiry->status == 2) Closed
                        @else Unknown
                        @endif
                    </span>
                </div>
                
                <div>
                    <span class="text-sm font-medium text-gray-600">Enquired On:</span>
                    <p class="text-base text-gray-800">
                        {{ $enquiry->enquired_at ? \Carbon\Carbon::parse($enquiry->enquired_at)->format('M d, Y H:i A') : 'N/A' }}
                    </p>
                </div>
                
                <div>
                    <span class="text-sm font-medium text-gray-600">Last Updated:</span>
                    <p class="text-base text-gray-800">
                        {{ \Carbon\Carbon::parse($enquiry->updated_at)->format('M d, Y H:i A') }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="mt-6 flex gap-4">
        <a
            href="{{ route('admin.enquiries.vendor.index') }}"
            class="secondary-button border-zinc-200 px-6 py-2"
        >
            @lang('admin::app.enquiries.vendor.show.back-to-enquiries')
        </a>
    </div>

</x-admin::layouts>

@pushOnce('scripts')
<script>
    function updateStatus(status) {
        const enquiryId = {{ $enquiry->id }};
        
        fetch(`{{ route('admin.enquiries.vendor.update-status', '') }}/${enquiryId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({ status: parseInt(status) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                // Show success message
                showNotification(data.message, 'success');
                // Reload page to update status display
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error updating status:', error);
            showNotification('Error updating enquiry status', 'error');
        });
    }

    function showNotification(message, type) {
        // Simple notification - you can enhance this with your notification system
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg text-white z-50 ${
            type === 'success' ? 'bg-green-500' : 'bg-red-500'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
</script>
@endPushOnce
